import{c as A,b as i,d as v,K as $,r as f,e as l,u as t,f as _,F as U,Z as E,h as n,s as F,q as u,j as d,k as P,l as x,E as I,t as O}from"./app-106e7db1.js";import{_ as Q,a as Y}from"./AdminLayout-fd7c0efe.js";import{_ as c,a as m}from"./TextInput-566b8743.js";import{_ as r}from"./InputLabel-a6c15f23.js";import{P as W}from"./PrimaryButton-71d6fb9f.js";import{_ as H}from"./TextArea-4aff7158.js";import{M as D}from"./Modal-95b9c727.js";import{_ as M}from"./SecondaryButton-e9368969.js";import{D as K}from"./DangerButton-d7fb5ad6.js";import{_ as R}from"./SearchableDropdownNew-4174a3dc.js";import{_ as Z}from"./MultipleFileUpload-e7dc9a5a.js";import{u as G}from"./index-a0116c9c.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const J={class:"w-full items-center"},X=["src"],ee=["src"],te={key:2},oe={__name:"FileViewer",props:{fileUrl:{type:String}},setup(y){const a=y,g=A(()=>{const e=a.fileUrl.split(".").pop().toLowerCase();return e==="pdf"?"pdf":["jpg","jpeg","png"].includes(e)?"image":"unsupported"});return(e,k)=>(i(),v("div",J,[g.value==="pdf"?(i(),v("iframe",{key:0,src:y.fileUrl,width:"100%",height:"500px",style:{"max-width":"100%","max-height":"500px","overflow-y":"auto"}},null,8,X)):g.value==="image"?(i(),v("img",{key:1,src:y.fileUrl,alt:"Image",style:{"max-width":"100%","max-height":"500px","overflow-y":"auto"}},null,8,ee)):(i(),v("p",te,"No Image"))]))}},se={class:"animate-top"},ne={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},le=n("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Lead",-1),ie=["onSubmit"],ae={class:"border-b border-gray-900/10 pb-12"},de={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},re={class:"sm:col-span-2"},me={class:"sm:col-span-2"},ue={class:"relative mt-2"},pe={class:"sm:col-span-2"},ce={class:"sm:col-span-2"},_e={class:"sm:col-span-2"},ve={class:"sm:col-span-2"},ye={class:"sm:col-span-2"},ge={class:"sm:col-span-2"},fe={class:"sm:col-span-2"},xe={class:"sm:col-span-2"},he={class:"sm:col-span-2"},ke={class:"sm:col-span-1"},we={class:"sm:col-span-1"},Ve={class:"sm:col-span-1"},be={class:"sm:col-span-1"},qe={class:"sm:col-span-2"},Ce={class:"sm:col-span-6"},$e={key:0,class:"sm:col-span-6 bg-white p-1 shadow sm:rounded-lg border"},Ue={class:"min-w-full divide-y divide-gray-300"},De=n("thead",{class:"bg-gray-50"},[n("tr",null,[n("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"}," UPLOADED FILES "),n("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),Me={class:"divide-y divide-gray-300 bg-white"},Se={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},je={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},ze=["onClick"],Le=n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Ne=[Le],Be=["onClick"],Te=n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Ae=[Te],Ee=["onClick"],Fe=n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1),Pe=[Fe],Ie={class:"flex mt-6 items-center justify-between"},Oe={class:"ml-auto flex items-center justify-end gap-x-6"},Qe=n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),Ye={key:0,class:"text-sm text-gray-600"},We={class:"p-6"},He=n("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1),Ke={class:"mt-6 flex justify-end"},Re={class:"p-6"},Ze={class:"mt-6 px-4 flex justify-end"},pt={__name:"Edit",props:{data:{type:Object},counties:{type:Array,required:!0},filepath:{type:String,required:!0}},setup(y){const a=$().props.data,g=$().props.filepath.view,e=G("post","/leads",{id:a.id,client_name:a.client_name,county_id:a.county_id,dimensions:a.dimensions,open_size:a.open_size,box_style:a.box_style,stock:a.stock,lamination:a.lamination,printing:a.printing,add_ons:a.add_ons,qty_1:a.qty_1,qty_2:a.qty_2,qty_3:a.qty_3,qty_4:a.qty_4,notes:a.notes,document:""}),k=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),S=(p,o)=>{e.county_id=p},j=p=>{e.document=p},w=f(!1),V=f(null),z=p=>{V.value=p,w.value=!0},L=()=>{e.get(route("removedocument",{id:V.value}),{onSuccess:()=>{closeDocumentModal()}})},h=f(!1),b=f(null),N=f("custom"),B=p=>{b.value=p,h.value=!0},q=()=>{h.value=!1},T=p=>{const o=window.location.origin+g+p,s=document.createElement("a");s.href=o,s.setAttribute("download",p),document.body.appendChild(s),s.click(),document.body.removeChild(s)};return(p,o)=>(i(),v(U,null,[l(t(E),{title:"Leads"}),l(Q,null,{default:_(()=>[n("div",se,[n("div",ne,[le,n("form",{onSubmit:F(k,["prevent"]),class:""},[n("div",ae,[n("div",de,[n("div",re,[l(r,{for:"client_name",value:"Client Name *"}),l(c,{id:"client_name",type:"text",modelValue:t(e).client_name,"onUpdate:modelValue":o[0]||(o[0]=s=>t(e).client_name=s),required:"",onChange:o[1]||(o[1]=s=>t(e).validate("client_name"))},null,8,["modelValue"]),t(e).invalid("client_name")?(i(),u(m,{key:0,message:t(e).errors.client_name},null,8,["message"])):d("",!0)]),n("div",me,[l(r,{for:"county_id",value:"Country *"}),n("div",ue,[l(R,{options:y.counties,modelValue:t(e).county_id,"onUpdate:modelValue":o[2]||(o[2]=s=>t(e).county_id=s),onOnchange:S,required:"",placeholder:"Select Country"},null,8,["options","modelValue"])]),t(e).invalid("county_id")?(i(),u(m,{key:0,message:t(e).errors.county_id},null,8,["message"])):d("",!0)]),n("div",pe,[l(r,{for:"email",value:"Email"}),l(c,{id:"email",type:"text",modelValue:t(e).email,"onUpdate:modelValue":o[3]||(o[3]=s=>t(e).email=s),onChange:o[4]||(o[4]=s=>t(e).validate("email"))},null,8,["modelValue"]),t(e).invalid("email")?(i(),u(m,{key:0,message:t(e).errors.email},null,8,["message"])):d("",!0)]),n("div",ce,[l(r,{for:"number",value:"Number"}),l(c,{id:"number",type:"text",modelValue:t(e).number,"onUpdate:modelValue":o[5]||(o[5]=s=>t(e).number=s),onChange:o[6]||(o[6]=s=>t(e).validate("number"))},null,8,["modelValue"]),t(e).invalid("number")?(i(),u(m,{key:0,message:t(e).errors.number},null,8,["message"])):d("",!0)]),n("div",_e,[l(r,{for:"dimensions",value:"Dimensions *"}),l(c,{id:"dimensions",type:"text",modelValue:t(e).dimensions,"onUpdate:modelValue":o[7]||(o[7]=s=>t(e).dimensions=s),required:"",onChange:o[8]||(o[8]=s=>t(e).validate("dimensions"))},null,8,["modelValue"]),t(e).invalid("dimensions")?(i(),u(m,{key:0,message:t(e).errors.dimensions},null,8,["message"])):d("",!0)]),n("div",ve,[l(r,{for:"open_size",value:"Open Size  *"}),l(c,{id:"open_size",type:"text",modelValue:t(e).open_size,"onUpdate:modelValue":o[9]||(o[9]=s=>t(e).open_size=s),required:"",onChange:o[10]||(o[10]=s=>t(e).validate("open_size"))},null,8,["modelValue"]),t(e).invalid("open_size")?(i(),u(m,{key:0,message:t(e).errors.open_size},null,8,["message"])):d("",!0)]),n("div",ye,[l(r,{for:"box_style",value:"Box Style *"}),l(c,{id:"box_style",type:"text",modelValue:t(e).box_style,"onUpdate:modelValue":o[11]||(o[11]=s=>t(e).box_style=s),required:"",onChange:o[12]||(o[12]=s=>t(e).validate("box_style"))},null,8,["modelValue"]),t(e).invalid("box_style")?(i(),u(m,{key:0,message:t(e).errors.box_style},null,8,["message"])):d("",!0)]),n("div",ge,[l(r,{for:"stock",value:"Stock *"}),l(c,{id:"stock",type:"text",modelValue:t(e).stock,"onUpdate:modelValue":o[13]||(o[13]=s=>t(e).stock=s),required:"",onChange:o[14]||(o[14]=s=>t(e).validate("stock"))},null,8,["modelValue"]),t(e).invalid("stock")?(i(),u(m,{key:0,message:t(e).errors.stock},null,8,["message"])):d("",!0)]),n("div",fe,[l(r,{for:"lamination",value:"Lamination *"}),l(c,{id:"lamination",type:"text",modelValue:t(e).lamination,"onUpdate:modelValue":o[15]||(o[15]=s=>t(e).lamination=s),required:"",onChange:o[16]||(o[16]=s=>t(e).validate("lamination"))},null,8,["modelValue"]),t(e).invalid("lamination")?(i(),u(m,{key:0,message:t(e).errors.lamination},null,8,["message"])):d("",!0)]),n("div",xe,[l(r,{for:"printing",value:"Printing *"}),l(c,{id:"printing",type:"text",modelValue:t(e).printing,"onUpdate:modelValue":o[17]||(o[17]=s=>t(e).printing=s),required:"",onChange:o[18]||(o[18]=s=>t(e).validate("printing"))},null,8,["modelValue"]),t(e).invalid("printing")?(i(),u(m,{key:0,message:t(e).errors.printing},null,8,["message"])):d("",!0)]),n("div",he,[l(r,{for:"add_ons",value:"Add ons"}),l(c,{id:"add_ons",type:"text",modelValue:t(e).add_ons,"onUpdate:modelValue":o[19]||(o[19]=s=>t(e).add_ons=s),onChange:o[20]||(o[20]=s=>t(e).validate("add_ons"))},null,8,["modelValue"]),t(e).invalid("add_ons")?(i(),u(m,{key:0,message:t(e).errors.add_ons},null,8,["message"])):d("",!0)]),n("div",ke,[l(r,{for:"qty_1",value:"QTY 1 *"}),l(c,{id:"qty_1",type:"text",numeric:!0,modelValue:t(e).qty_1,"onUpdate:modelValue":o[21]||(o[21]=s=>t(e).qty_1=s),required:"",onChange:o[22]||(o[22]=s=>t(e).validate("qty_1"))},null,8,["modelValue"]),t(e).invalid("qty_1")?(i(),u(m,{key:0,message:t(e).errors.qty_1},null,8,["message"])):d("",!0)]),n("div",we,[l(r,{for:"qty_2",value:"QTY 2"}),l(c,{id:"qty_2",type:"text",numeric:!0,modelValue:t(e).qty_2,"onUpdate:modelValue":o[23]||(o[23]=s=>t(e).qty_2=s),onChange:o[24]||(o[24]=s=>t(e).validate("qty_2"))},null,8,["modelValue"]),t(e).invalid("qty_2")?(i(),u(m,{key:0,message:t(e).errors.qty_2},null,8,["message"])):d("",!0)]),n("div",Ve,[l(r,{for:"qty_3",value:"QTY 3"}),l(c,{id:"qty_3",type:"text",numeric:!0,modelValue:t(e).qty_3,"onUpdate:modelValue":o[25]||(o[25]=s=>t(e).qty_3=s),onChange:o[26]||(o[26]=s=>t(e).validate("qty_3"))},null,8,["modelValue"]),t(e).invalid("qty_3")?(i(),u(m,{key:0,message:t(e).errors.qty_3},null,8,["message"])):d("",!0)]),n("div",be,[l(r,{for:"qty_4",value:"QTY 4"}),l(c,{id:"qty_4",type:"text",numeric:!0,modelValue:t(e).qty_4,"onUpdate:modelValue":o[27]||(o[27]=s=>t(e).qty_4=s),onChange:o[28]||(o[28]=s=>t(e).validate("qty_4"))},null,8,["modelValue"]),t(e).invalid("qty_4")?(i(),u(m,{key:0,message:t(e).errors.qty_4},null,8,["message"])):d("",!0)]),n("div",qe,[l(r,{for:"note",value:"File Upload"}),l(Z,{inputId:"document",inputName:"document",onFiles:j})]),n("div",Ce,[l(r,{for:"notes",value:"Notes"}),l(H,{id:"notes",type:"text",rows:3,modelValue:t(e).notes,"onUpdate:modelValue":o[29]||(o[29]=s=>t(e).notes=s),autocomplete:"notes",onChange:o[30]||(o[30]=s=>t(e).validate("notes"))},null,8,["modelValue"]),l(m,{class:"",message:t(e).errors.notes},null,8,["message"])]),t(a).documents&&t(a).documents.length>0?(i(),v("div",$e,[n("table",Ue,[De,n("tbody",Me,[(i(!0),v(U,null,P(t(a).documents,(s,Ge)=>(i(),v("tr",{key:t(a).id,class:""},[n("td",Se,O(s.orignal_name),1),n("td",je,[n("button",{type:"button",onClick:C=>z(s.id)},Ne,8,ze),n("button",{type:"button",onClick:C=>B(s.name)},Ae,8,Be),n("button",{type:"button",onClick:C=>T(s.name)},Pe,8,Ee)])]))),128))])])])):d("",!0)])]),n("div",Ie,[n("div",Oe,[l(Y,{href:p.route("leads.index")},{svg:_(()=>[Qe]),_:1},8,["href"]),l(W,{disabled:t(e).processing},{default:_(()=>[x("Save")]),_:1},8,["disabled"]),l(I,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:_(()=>[t(e).recentlySuccessful?(i(),v("p",Ye,"Saved.")):d("",!0)]),_:1})])])],40,ie)])]),l(D,{show:w.value,onClose:p.closeDocumentModal},{default:_(()=>[n("div",We,[He,n("div",Ke,[l(M,{onClick:p.closeDocumentModal},{default:_(()=>[x(" Cancel")]),_:1},8,["onClick"]),l(K,{class:"ml-3",onClick:L},{default:_(()=>[x(" Delete ")]),_:1})])])]),_:1},8,["show","onClose"]),l(D,{show:h.value,onClose:q,maxWidth:N.value},{default:_(()=>[n("div",Re,[l(oe,{fileUrl:t(g)+b.value},null,8,["fileUrl"]),n("div",Ze,[l(M,{onClick:q},{default:_(()=>[x(" Cancel")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}};export{pt as default};
