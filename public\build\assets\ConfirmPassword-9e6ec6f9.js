import{T as d,b as l,q as m,f as r,e as o,u as e,Z as c,h as t,l as u,n as p,s as f}from"./app-106e7db1.js";import{G as _}from"./GuestLayout-4cf44b8b.js";import{_ as w,a as g}from"./TextInput-566b8743.js";import{_ as x}from"./InputLabel-a6c15f23.js";import{P as h}from"./PrimaryButton-71d6fb9f.js";import"./_plugin-vue_export-helper-c27b6911.js";const y=t("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Confirm Password",-1),b=t("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," This is a secure area of the application. Please confirm your password before continuing. ",-1),P=["onSubmit"],C={class:"flex justify-end mt-4"},G={__name:"ConfirmPassword",setup(V){const s=d({password:""}),i=()=>{s.post(route("password.confirm"),{onFinish:()=>s.reset()})};return(v,a)=>(l(),m(_,null,{default:r(()=>[o(e(c),{title:"Confirm Password"}),y,b,t("form",{onSubmit:f(i,["prevent"])},[t("div",null,[o(x,{for:"password",value:"Password"}),o(w,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password,"onUpdate:modelValue":a[0]||(a[0]=n=>e(s).password=n),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),o(g,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),t("div",C,[o(h,{class:p(["",{"opacity-25":e(s).processing}]),disabled:e(s).processing},{default:r(()=>[u(" Confirm ")]),_:1},8,["class","disabled"])])],40,P)]),_:1}))}};export{G as default};
