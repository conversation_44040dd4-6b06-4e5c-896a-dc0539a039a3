import{r,w,o as C,a as V,b as u,d as c,i as B,v as z,h as s,F as D,k as E,n as f,t as L,j as y}from"./app-106e7db1.js";const S=s("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[s("path",{"fill-rule":"evenodd",d:"M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z","clip-rule":"evenodd"})],-1),F=[S],N={key:0,class:"absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm cursor-pointer",id:"options",role:"listbox","aria-labelledby":"combobox"},T=["onClick","onMouseenter"],I=s("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[s("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z","clip-rule":"evenodd"})],-1),U=[I],j={__name:"SearchableDropdownNew",props:["options","modelValue","editMode"],emits:["onchange"],setup(_,{emit:h}){const n=_,p=r(n.options),o=r(""),d=r(!1),i=r(-1),m=r(!1),v=()=>{const e=new RegExp(o.value,"i");p.value=n.options.filter(t=>e.test(t.name))},g=()=>{o.value="",v()},k=(e,t)=>{o.value=e,d.value=!1,m.value=!1,h("update:modelValue",t),h("onchange",t,e)},b=()=>{n.editMode||(d.value=!0),m.value||g()},M=e=>{i.value=e};w(()=>n.options,()=>{v()}),w(()=>n.modelValue,e=>{const t=n.options.find(l=>l.id===e);t&&(o.value=t.name)}),C(()=>{const e=n.options.find(t=>t.id===n.modelValue);e?(o.value=e.name,m.value=!1):g(),document.addEventListener("click",x)}),V(()=>{document.removeEventListener("click",x)});const x=e=>{e.target.closest(".relative")||(d.value=!1)};return(e,t)=>(u(),c("div",null,[B(s("input",{id:"combobox",type:"text",placeholder:"Search...",role:"combobox","onUpdate:modelValue":t[0]||(t[0]=l=>o.value=l),onInput:v,onFocus:b,autocomplete:"off",class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-7 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,544),[[z,o.value]]),s("button",{type:"button",class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none",onClick:b},F),d.value&&p.value.length?(u(),c("ul",N,[(u(!0),c(D,null,E(p.value,(l,a)=>(u(),c("li",{class:f(["relative cursor-default select-none py-2 pl-3 pr-9 cursor-pointer",{"text-white bg-indigo-600":i.value===a,"text-gray-900":i.value!==a}]),key:a,onClick:O=>k(l.name,l.id),onMouseenter:O=>M(a),tabindex:"-1",role:"option"},[s("span",{class:f(["block truncate",{"font-semibold":l.name===o.value}])},L(l.name),3),l.name===o.value?(u(),c("span",{key:0,class:f(["absolute inset-y-0 right-0 flex items-center pr-4",{"text-white":i.value===a,"text-indigo-600":i.value!==a}])},U,2)):y("",!0)],42,T))),128))])):y("",!0)]))}};export{j as _};
