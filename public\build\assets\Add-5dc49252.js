import{T as I,r as k,w as G,b as u,d as _,e as o,u as d,f as F,F as H,Z,h as e,l as K,t as c,j as m,s as J}from"./app-106e7db1.js";import{_ as R,a as W}from"./AdminLayout-fd7c0efe.js";import{_ as C,a as y}from"./TextInput-566b8743.js";import{_ as r}from"./InputLabel-a6c15f23.js";import{P as X}from"./PrimaryButton-71d6fb9f.js";import{_ as Y}from"./TextArea-4aff7158.js";import{_ as S}from"./Checkbox-211f1c19.js";import"./_plugin-vue_export-helper-c27b6911.js";const ee={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},te=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"}," Convert Quotation to Order ",-1),se={class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},le={class:"text-sm text-blue-800"},oe=e("strong",null,"Converting from Quotation:",-1),ae=e("div",{class:"sm:col-span-12 mt-6"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information")],-1),de={class:"mt-6 p-4 bg-gray-50 rounded-lg"},ne={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ie=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1),ce={class:"text-sm text-gray-700"},re=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1),ue={class:"text-sm text-gray-700"},_e=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1),me={class:"text-sm text-gray-700"},ye=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1),qe={class:"text-sm text-gray-700"},xe=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1),ge={class:"text-sm text-gray-700"},ve=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1),fe={class:"text-sm text-gray-700"},be=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1),pe={class:"text-sm text-gray-700"},he=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1),ke={class:"text-sm text-gray-700"},Ce={key:0},Ve=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1),we={class:"text-sm text-gray-700"},Ae=["onSubmit"],Ue={class:"border-b border-gray-900/10 pb-12"},Ne={class:"mt-6 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},$e=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4"},"Select Quantities for Order"),e("p",{class:"text-sm text-gray-600"},"Select the quantities that the client needs. Check the boxes and enter the required amounts:")],-1),Qe={key:0,class:"sm:col-span-3"},Se={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},De={class:"grid grid-cols-1 gap-4"},Fe={class:"text-sm text-gray-500 mt-1"},Oe={key:1,class:"sm:col-span-3"},Be={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Pe={class:"grid grid-cols-1 gap-4"},Me={class:"text-sm text-gray-500 mt-1"},je={key:2,class:"sm:col-span-3"},ze={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},Te={class:"grid grid-cols-1 gap-4"},Ie={class:"text-sm text-gray-500 mt-1"},Ke={key:3,class:"sm:col-span-3"},Le={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ee={class:"grid grid-cols-1 gap-4"},Ge={class:"text-sm text-gray-500 mt-1"},He={key:4,class:"sm:col-span-12"},Ze={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Je={class:"flex items-center justify-between"},Re={class:"text-lg font-semibold text-green-800"},We=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing from quotation ",-1),Xe=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1),Ye=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4 mb-4"},"Order Information")],-1),et={class:"sm:col-span-6"},tt={class:"sm:col-span-12"},st={class:"flex mt-6 items-center justify-between"},lt={class:"ml-auto flex items-center justify-end gap-x-6"},ot=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),mt={__name:"Add",props:{quotation:{type:Object,required:!0}},setup(s){var B,P,M,j;const i=s,t=I({quotation_id:i.quotation.id,lead_id:i.quotation.lead_id,selected_qty_1:((B=i.quotation.lead)==null?void 0:B.qty_1)||"",selected_qty_2:((P=i.quotation.lead)==null?void 0:P.qty_2)||"",selected_qty_3:((M=i.quotation.lead)==null?void 0:M.qty_3)||"",selected_qty_4:((j=i.quotation.lead)==null?void 0:j.qty_4)||"",notes:"",expected_delivery:"",tracking_number:""}),q=k(!1),x=k(!1),g=k(!1),v=k(!1),V=k(0),L=()=>{const n={quotation_id:t.quotation_id,lead_id:t.lead_id,notes:t.notes,expected_delivery:t.expected_delivery,tracking_number:t.tracking_number,selected_qty_1:null,selected_qty_2:null,selected_qty_3:null,selected_qty_4:null};return q.value&&t.selected_qty_1&&(n.selected_qty_1=t.selected_qty_1),x.value&&t.selected_qty_2&&(n.selected_qty_2=t.selected_qty_2),g.value&&t.selected_qty_3&&(n.selected_qty_3=t.selected_qty_3),v.value&&t.selected_qty_4&&(n.selected_qty_4=t.selected_qty_4),n},E=()=>{const n=L();I(n).post(route("orders.store"),{preserveScroll:!0,onSuccess:()=>t.reset()})},O=()=>{let n=0;q.value&&t.selected_qty_1&&i.quotation.price_qty_1&&(n+=parseFloat(t.selected_qty_1)*parseFloat(i.quotation.price_qty_1)),x.value&&t.selected_qty_2&&i.quotation.price_qty_2&&(n+=parseFloat(t.selected_qty_2)*parseFloat(i.quotation.price_qty_2)),g.value&&t.selected_qty_3&&i.quotation.price_qty_3&&(n+=parseFloat(t.selected_qty_3)*parseFloat(i.quotation.price_qty_3)),v.value&&t.selected_qty_4&&i.quotation.price_qty_4&&(n+=parseFloat(t.selected_qty_4)*parseFloat(i.quotation.price_qty_4)),V.value=n},w=(n,l)=>{O()};G([()=>t.selected_qty_1,()=>q.value,()=>t.selected_qty_2,()=>x.value,()=>t.selected_qty_3,()=>g.value,()=>t.selected_qty_4,()=>v.value],O);const D=new Date;D.setDate(D.getDate()+7),t.expected_delivery=D.toISOString().split("T")[0];const f=n=>{var p,h;const l={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},A=((h=(p=i.quotation.lead)==null?void 0:p.county)==null?void 0:h.name)||"UK",U=Object.keys(l).find(Q=>A.toLowerCase().includes(Q.toLowerCase())),{locale:N,currency:b}=l[U]||l.UK,$=new Intl.NumberFormat(N,{style:"currency",currency:b,currencyDisplay:"symbol"}).format(n);return`${b} ${$}`};return(n,l)=>(u(),_(H,null,[o(d(Z),{title:"Orders"}),o(R,null,{default:F(()=>{var A,U,N,b,$,p,h,Q,z,T;return[e("div",ee,[te,e("div",se,[e("p",le,[oe,K(" "+c(s.quotation.quotation_number),1)])]),ae,e("div",de,[e("div",ne,[e("div",null,[ie,e("p",ce,c(((A=s.quotation.lead)==null?void 0:A.client_name)||"N/A"),1)]),e("div",null,[re,e("p",ue,c(((N=(U=s.quotation.lead)==null?void 0:U.county)==null?void 0:N.name)||"N/A"),1)]),e("div",null,[_e,e("p",me,c(((b=s.quotation.lead)==null?void 0:b.dimensions)||"N/A"),1)]),e("div",null,[ye,e("p",qe,c((($=s.quotation.lead)==null?void 0:$.open_size)||"N/A"),1)]),e("div",null,[xe,e("p",ge,c(((p=s.quotation.lead)==null?void 0:p.box_style)||"N/A"),1)]),e("div",null,[ve,e("p",fe,c(((h=s.quotation.lead)==null?void 0:h.stock)||"N/A"),1)]),e("div",null,[be,e("p",pe,c(((Q=s.quotation.lead)==null?void 0:Q.lamination)||"N/A"),1)]),e("div",null,[he,e("p",ke,c(((z=s.quotation.lead)==null?void 0:z.printing)||"N/A"),1)]),(T=s.quotation.lead)!=null&&T.add_ons?(u(),_("div",Ce,[Ve,e("p",we,c(s.quotation.lead.add_ons),1)])):m("",!0)])]),e("form",{onSubmit:J(E,["prevent"])},[e("div",Ue,[e("div",Ne,[$e,s.quotation.lead.qty_1&&s.quotation.price_qty_1?(u(),_("div",Qe,[e("div",Se,[o(S,{checked:q.value,"onUpdate:checked":[l[0]||(l[0]=a=>q.value=a),l[1]||(l[1]=a=>w(1,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",De,[e("div",null,[o(r,{for:"selected_qty_1",value:`Client Order Qty 1 (Available: ${s.quotation.lead.qty_1})`},null,8,["value"]),o(C,{id:"selected_qty_1",type:"number",modelValue:d(t).selected_qty_1,"onUpdate:modelValue":l[2]||(l[2]=a=>d(t).selected_qty_1=a),max:s.quotation.lead.qty_1,min:"1",placeholder:`Max: ${s.quotation.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_1},null,8,["message"]),e("p",Fe,"Price: "+c(f(s.quotation.price_qty_1))+" per unit",1)])])])):m("",!0),s.quotation.lead.qty_2&&s.quotation.price_qty_2?(u(),_("div",Oe,[e("div",Be,[o(S,{checked:x.value,"onUpdate:checked":[l[3]||(l[3]=a=>x.value=a),l[4]||(l[4]=a=>w(2,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",Pe,[e("div",null,[o(r,{for:"selected_qty_2",value:`Client Order Qty 2 (Available: ${s.quotation.lead.qty_2})`},null,8,["value"]),o(C,{id:"selected_qty_2",type:"number",modelValue:d(t).selected_qty_2,"onUpdate:modelValue":l[5]||(l[5]=a=>d(t).selected_qty_2=a),max:s.quotation.lead.qty_2,min:"1",placeholder:`Max: ${s.quotation.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_2},null,8,["message"]),e("p",Me,"Price: "+c(f(s.quotation.price_qty_2))+" per unit",1)])])])):m("",!0),s.quotation.lead.qty_3&&s.quotation.price_qty_3?(u(),_("div",je,[e("div",ze,[o(S,{checked:g.value,"onUpdate:checked":[l[6]||(l[6]=a=>g.value=a),l[7]||(l[7]=a=>w(3,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",Te,[e("div",null,[o(r,{for:"selected_qty_3",value:`Client Order Qty 3 (Available: ${s.quotation.lead.qty_3})`},null,8,["value"]),o(C,{id:"selected_qty_3",type:"number",modelValue:d(t).selected_qty_3,"onUpdate:modelValue":l[8]||(l[8]=a=>d(t).selected_qty_3=a),max:s.quotation.lead.qty_3,min:"1",placeholder:`Max: ${s.quotation.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_3},null,8,["message"]),e("p",Ie,"Price: "+c(f(s.quotation.price_qty_3))+" per unit",1)])])])):m("",!0),s.quotation.lead.qty_4&&s.quotation.price_qty_4?(u(),_("div",Ke,[e("div",Le,[o(S,{checked:v.value,"onUpdate:checked":[l[9]||(l[9]=a=>v.value=a),l[10]||(l[10]=a=>w(4,a))]},null,8,["checked"]),o(r,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",Ee,[e("div",null,[o(r,{for:"selected_qty_4",value:`Client Order Qty 4 (Available: ${s.quotation.lead.qty_4})`},null,8,["value"]),o(C,{id:"selected_qty_4",type:"number",modelValue:d(t).selected_qty_4,"onUpdate:modelValue":l[11]||(l[11]=a=>d(t).selected_qty_4=a),max:s.quotation.lead.qty_4,min:"1",placeholder:`Max: ${s.quotation.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(y,{message:d(t).errors.selected_qty_4},null,8,["message"]),e("p",Ge,"Price: "+c(f(s.quotation.price_qty_4))+" per unit",1)])])])):m("",!0),V.value>0?(u(),_("div",He,[e("div",Ze,[e("div",Je,[e("div",null,[e("p",Re," Order Total: "+c(f(V.value.toFixed(2))),1),We]),Xe])])])):m("",!0),Ye,e("div",et,[o(r,{for:"expected_delivery",value:"Expected Delivery Date"}),o(C,{id:"expected_delivery",type:"date",modelValue:d(t).expected_delivery,"onUpdate:modelValue":l[12]||(l[12]=a=>d(t).expected_delivery=a)},null,8,["modelValue"]),o(y,{message:d(t).errors.expected_delivery},null,8,["message"])]),e("div",tt,[o(r,{for:"notes",value:"Order Notes"}),o(Y,{id:"notes",modelValue:d(t).notes,"onUpdate:modelValue":l[13]||(l[13]=a=>d(t).notes=a),rows:"4",placeholder:"Any special instructions or notes for this order..."},null,8,["modelValue"]),o(y,{message:d(t).errors.notes},null,8,["message"])])])]),e("div",st,[e("div",lt,[o(W,{href:n.route("quotations.show",s.quotation.id)},{svg:F(()=>[ot]),_:1},8,["href"]),o(X,{disabled:d(t).processing||V.value===0},{default:F(()=>[K(" Save ")]),_:1},8,["disabled"])])])],40,Ae)])]}),_:1})],64))}};export{mt as default};
