import{b as a,d as _,e as o,u as s,f as v,F as q,Z as x,h as l,s as c,q as r,j as d,l as b,E as k}from"./app-106e7db1.js";import{_ as $,a as C}from"./AdminLayout-fd7c0efe.js";import{_ as u,a as m}from"./TextInput-566b8743.js";import{_ as i}from"./InputLabel-a6c15f23.js";import{P as U}from"./PrimaryButton-71d6fb9f.js";import{_ as S}from"./TextArea-4aff7158.js";import{_ as z}from"./SearchableDropdownNew-4174a3dc.js";import{_ as N}from"./MultipleFileUpload-e7dc9a5a.js";import{u as w}from"./index-a0116c9c.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top"},T={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},A=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Lead",-1),F=["onSubmit"],Q={class:"border-b border-gray-900/10 pb-12"},Y={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},j={class:"sm:col-span-2"},D={class:"sm:col-span-2"},E={class:"relative mt-2"},L={class:"sm:col-span-2"},P={class:"sm:col-span-2"},O={class:"sm:col-span-2"},I={class:"sm:col-span-2"},M={class:"sm:col-span-2"},Z={class:"sm:col-span-2"},G={class:"sm:col-span-2"},H={class:"sm:col-span-2"},J={class:"sm:col-span-2"},K={class:"sm:col-span-1"},R={class:"sm:col-span-1"},W={class:"sm:col-span-1"},X={class:"sm:col-span-1"},h={class:"sm:col-span-2"},ee={class:"sm:col-span-6"},se={class:"flex mt-6 items-center justify-between"},te={class:"ml-auto flex items-center justify-end gap-x-6"},ne=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),oe={key:0,class:"text-sm text-gray-600"},ye={__name:"Add",props:{counties:{type:Array,required:!0}},setup(y){const e=w("post","/leads",{client_name:"",email:"",number:"",county_id:"",dimensions:"",open_size:"",box_style:"",stock:"",lamination:"",printing:"",add_ons:"",qty_1:"",qty_2:"",qty_3:"",qty_4:"",notes:"",document:""}),g=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),f=(p,t)=>{e.county_id=p},V=p=>{e.document=p};return(p,t)=>(a(),_(q,null,[o(s(x),{title:"Leads"}),o($,null,{default:v(()=>[l("div",B,[l("div",T,[A,l("form",{onSubmit:c(g,["prevent"]),class:""},[l("div",Q,[l("div",Y,[l("div",j,[o(i,{for:"client_name",value:"Client Name *"}),o(u,{id:"client_name",type:"text",modelValue:s(e).client_name,"onUpdate:modelValue":t[0]||(t[0]=n=>s(e).client_name=n),required:"",onChange:t[1]||(t[1]=n=>s(e).validate("client_name"))},null,8,["modelValue"]),s(e).invalid("client_name")?(a(),r(m,{key:0,message:s(e).errors.client_name},null,8,["message"])):d("",!0)]),l("div",D,[o(i,{for:"county_id",value:"Country *"}),l("div",E,[o(z,{options:y.counties,onOnchange:f,required:"",placeholder:"Select Country"},null,8,["options"])]),s(e).invalid("county_id")?(a(),r(m,{key:0,message:s(e).errors.county_id},null,8,["message"])):d("",!0)]),l("div",L,[o(i,{for:"email",value:"Email"}),o(u,{id:"email",type:"text",modelValue:s(e).email,"onUpdate:modelValue":t[2]||(t[2]=n=>s(e).email=n),onChange:t[3]||(t[3]=n=>s(e).validate("email"))},null,8,["modelValue"]),s(e).invalid("email")?(a(),r(m,{key:0,message:s(e).errors.email},null,8,["message"])):d("",!0)]),l("div",P,[o(i,{for:"number",value:"Number"}),o(u,{id:"number",type:"text",modelValue:s(e).number,"onUpdate:modelValue":t[4]||(t[4]=n=>s(e).number=n),onChange:t[5]||(t[5]=n=>s(e).validate("number"))},null,8,["modelValue"]),s(e).invalid("number")?(a(),r(m,{key:0,message:s(e).errors.number},null,8,["message"])):d("",!0)]),l("div",O,[o(i,{for:"dimensions",value:"Dimensions *"}),o(u,{id:"dimensions",type:"text",modelValue:s(e).dimensions,"onUpdate:modelValue":t[6]||(t[6]=n=>s(e).dimensions=n),required:"",onChange:t[7]||(t[7]=n=>s(e).validate("dimensions"))},null,8,["modelValue"]),s(e).invalid("dimensions")?(a(),r(m,{key:0,message:s(e).errors.dimensions},null,8,["message"])):d("",!0)]),l("div",I,[o(i,{for:"open_size",value:"Open Size  *"}),o(u,{id:"open_size",type:"text",modelValue:s(e).open_size,"onUpdate:modelValue":t[8]||(t[8]=n=>s(e).open_size=n),required:"",onChange:t[9]||(t[9]=n=>s(e).validate("open_size"))},null,8,["modelValue"]),s(e).invalid("open_size")?(a(),r(m,{key:0,message:s(e).errors.open_size},null,8,["message"])):d("",!0)]),l("div",M,[o(i,{for:"box_style",value:"Box Style *"}),o(u,{id:"box_style",type:"text",modelValue:s(e).box_style,"onUpdate:modelValue":t[10]||(t[10]=n=>s(e).box_style=n),required:"",onChange:t[11]||(t[11]=n=>s(e).validate("box_style"))},null,8,["modelValue"]),s(e).invalid("box_style")?(a(),r(m,{key:0,message:s(e).errors.box_style},null,8,["message"])):d("",!0)]),l("div",Z,[o(i,{for:"stock",value:"Stock *"}),o(u,{id:"stock",type:"text",modelValue:s(e).stock,"onUpdate:modelValue":t[12]||(t[12]=n=>s(e).stock=n),required:"",onChange:t[13]||(t[13]=n=>s(e).validate("stock"))},null,8,["modelValue"]),s(e).invalid("stock")?(a(),r(m,{key:0,message:s(e).errors.stock},null,8,["message"])):d("",!0)]),l("div",G,[o(i,{for:"lamination",value:"Lamination *"}),o(u,{id:"lamination",type:"text",modelValue:s(e).lamination,"onUpdate:modelValue":t[14]||(t[14]=n=>s(e).lamination=n),required:"",onChange:t[15]||(t[15]=n=>s(e).validate("lamination"))},null,8,["modelValue"]),s(e).invalid("lamination")?(a(),r(m,{key:0,message:s(e).errors.lamination},null,8,["message"])):d("",!0)]),l("div",H,[o(i,{for:"printing",value:"Printing *"}),o(u,{id:"printing",type:"text",modelValue:s(e).printing,"onUpdate:modelValue":t[16]||(t[16]=n=>s(e).printing=n),required:"",onChange:t[17]||(t[17]=n=>s(e).validate("printing"))},null,8,["modelValue"]),s(e).invalid("printing")?(a(),r(m,{key:0,message:s(e).errors.printing},null,8,["message"])):d("",!0)]),l("div",J,[o(i,{for:"add_ons",value:"Add ons"}),o(u,{id:"add_ons",type:"text",modelValue:s(e).add_ons,"onUpdate:modelValue":t[18]||(t[18]=n=>s(e).add_ons=n),onChange:t[19]||(t[19]=n=>s(e).validate("add_ons"))},null,8,["modelValue"]),s(e).invalid("add_ons")?(a(),r(m,{key:0,message:s(e).errors.add_ons},null,8,["message"])):d("",!0)]),l("div",K,[o(i,{for:"qty_1",value:"QTY 1 *"}),o(u,{id:"qty_1",type:"text",modelValue:s(e).qty_1,"onUpdate:modelValue":t[20]||(t[20]=n=>s(e).qty_1=n),numeric:!0,required:"",onChange:t[21]||(t[21]=n=>s(e).validate("qty_1"))},null,8,["modelValue"]),s(e).invalid("qty_1")?(a(),r(m,{key:0,message:s(e).errors.qty_1},null,8,["message"])):d("",!0)]),l("div",R,[o(i,{for:"qty_2",value:"QTY 2"}),o(u,{id:"qty_2",type:"text",numeric:!0,modelValue:s(e).qty_2,"onUpdate:modelValue":t[22]||(t[22]=n=>s(e).qty_2=n),onChange:t[23]||(t[23]=n=>s(e).validate("qty_2"))},null,8,["modelValue"]),s(e).invalid("qty_2")?(a(),r(m,{key:0,message:s(e).errors.qty_2},null,8,["message"])):d("",!0)]),l("div",W,[o(i,{for:"qty_3",value:"QTY 3"}),o(u,{id:"qty_3",type:"text",numeric:!0,modelValue:s(e).qty_3,"onUpdate:modelValue":t[24]||(t[24]=n=>s(e).qty_3=n),onChange:t[25]||(t[25]=n=>s(e).validate("qty_3"))},null,8,["modelValue"]),s(e).invalid("qty_3")?(a(),r(m,{key:0,message:s(e).errors.qty_3},null,8,["message"])):d("",!0)]),l("div",X,[o(i,{for:"qty_4",value:"QTY 4"}),o(u,{id:"qty_4",type:"text",numeric:!0,modelValue:s(e).qty_4,"onUpdate:modelValue":t[26]||(t[26]=n=>s(e).qty_4=n),onChange:t[27]||(t[27]=n=>s(e).validate("qty_4"))},null,8,["modelValue"]),s(e).invalid("qty_4")?(a(),r(m,{key:0,message:s(e).errors.qty_4},null,8,["message"])):d("",!0)]),l("div",h,[o(i,{for:"note",value:"Upload Documents"}),o(N,{inputId:"document",inputName:"document",onFiles:V})]),l("div",ee,[o(i,{for:"notes",value:"Notes"}),o(S,{id:"notes",type:"text",rows:3,modelValue:s(e).notes,"onUpdate:modelValue":t[28]||(t[28]=n=>s(e).notes=n),autocomplete:"notes",onChange:t[29]||(t[29]=n=>s(e).validate("notes"))},null,8,["modelValue"]),o(m,{class:"",message:s(e).errors.notes},null,8,["message"])])])]),l("div",se,[l("div",te,[o(C,{href:p.route("leads.index")},{svg:v(()=>[ne]),_:1},8,["href"]),o(U,{disabled:s(e).processing},{default:v(()=>[b("Save")]),_:1},8,["disabled"]),o(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:v(()=>[s(e).recentlySuccessful?(a(),_("p",oe,"Saved.")):d("",!0)]),_:1})])])],40,F)])])]),_:1})],64))}};export{ye as default};
