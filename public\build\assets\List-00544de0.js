import{r as p,c as B,b as a,d as l,e as i,u as v,f as d,F as w,Z as de,h as e,i as L,v as ce,G as ue,n as V,l as x,j as h,k as A,q as j,t as n,z as W,y as q,H as me,O as R}from"./app-106e7db1.js";import{_ as he,b as _e,a as Y}from"./AdminLayout-fd7c0efe.js";import{_ as ge}from"./CreateButton-ad75d959.js";import{_ as pe}from"./SecondaryButton-e9368969.js";import{D as ve}from"./DangerButton-d7fb5ad6.js";import{M as Z}from"./Modal-95b9c727.js";import{s as xe,_ as fe}from"./Pagination-ab577825.js";import{_ as U}from"./SearchableDropdownNew-4174a3dc.js";import{_ as $}from"./InputLabel-a6c15f23.js";import{_ as be}from"./ArrowIcon-7e47bcc0.js";import{L as ye,C as we}from"./LeadComments-02a1a569.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ke={class:"animate-top"},Ce={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},Ae=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Leads")],-1),Se={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},Me={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},Le=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),Ve={class:"flex rounded-lg border border-gray-200 bg-white"},$e=e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})],-1),Ne=[$e],ze=e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h3M3 20h18a1 1 0 001-1V5a1 1 0 00-1-1H3a1 1 0 00-1 1v14a1 1 0 001 1z"})],-1),De=[ze],Ee={key:0,class:"sm:flex-none"},Be={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},je={class:"flex justify-between mb-2"},Ue={class:"flex"},Oe=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Te={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ie={key:0,class:"sm:col-span-4"},Pe={class:"relative mt-2"},He={class:"sm:col-span-4"},Fe={class:"relative mt-2"},Ge={class:"sm:col-span-4"},We={class:"relative mt-2"},qe={key:0,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8"},Re={class:"flex justify-between items-start"},Ye={class:"text-lg font-semibold text-gray-800"},Ze={class:"text-sm font-semibold text-gray-900"},Ke={class:"flex-shrink-0"},Qe={key:0},Xe=["onUpdate:modelValue","onChange"],Je=["value"],et=["onClick"],tt=["onClick"],st={class:"flex space-x-2 mt-2 justify-between items-center w-full"},ot={class:"text-xs text-gray-700"},at={class:"text-xs text-gray-700"},nt={class:"text-xs text-gray-700"},lt={class:"mt-2"},it={class:"text-sm"},rt=e("strong",null,"Open Size:",-1),dt={class:"text-sm"},ct=e("strong",null,"Email:",-1),ut={class:"text-sm"},mt=e("strong",null,"Number:",-1),ht={class:"flex space-x-2 mt-4"},_t=["onClick"],gt={key:1,class:"mt-8 overflow-x-auto rounded-lg max-w-full"},pt={class:"shadow rounded-lg"},vt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},xt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},ft={class:"border-b-2"},bt=["onClick"],yt={key:0},wt={class:"px-4 py-2.5 min-w-28"},kt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ct={class:"px-4 py-2.5"},At={class:"px-4 py-2.5 min-w-28"},St={class:"px-4 py-2.5 min-w-28"},Mt={class:"px-4 py-2.5 min-w-32"},Lt={class:"px-4 py-2.5 min-w-32"},Vt={class:"px-4 py-2.5"},$t={key:0,class:"flex items-center space-x-2 w-full"},Nt=["onUpdate:modelValue","onChange"],zt=["value"],Dt=["onClick"],Et={key:1,class:"flex items-center space-x-2"},Bt=["onClick"],jt={class:"px-4 py-2.5 min-w-28"},Ut={key:0,class:"px-4 py-2.5"},Ot={class:"items-center px-4 py-2.5"},Tt={class:"flex items-center justify-start gap-4"},It=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Pt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),Ht=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),Ft=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Gt=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Wt=["onClick"],qt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Rt=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Yt=[qt,Rt],Zt={key:1},Kt=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Qt=[Kt],Xt={class:"bg-white rounded-lg p-6 w-full"},Jt=e("h3",{class:"text-lg font-medium mb-4"},"Add Comment",-1),es={class:"p-6"},ts=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this lead? ",-1),ss={class:"mt-6 flex justify-end"},vs={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","status","isAdmin"],setup(u){const f=u,{form:N,search:S,sort:K,fetchData:os,sortKey:Q,sortDirection:X}=xe("leads.index"),z=p(!1),O=p(null),y=p("card"),D=[{id:"new",name:"New"},{id:"contacted",name:"Contacted"},{id:"quotation",name:"Quotation"},{id:"negotiation",name:"Negotiation"},{id:"won",name:"Won"}],J=B(()=>[{id:"",name:"All Agents"},...f.agents]),ee=B(()=>[{id:"",name:"All Country"},...f.counties]),te=B(()=>[{id:"",name:"All Status"},...D]),se=[{field:"lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!1,visible:!0},{field:"open_size",label:"OPEN SIZE",sortable:!0,visible:!0},{field:"box_style",label:"BOX STYLE",sortable:!0,visible:!0},{field:"email",label:"Email",sortable:!0,visible:!0},{field:"number",label:"Number",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"created_at",label:"DATE",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:f.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],T=s=>{O.value=s,z.value=!0},E=()=>{z.value=!1},oe=()=>{N.delete(route("leads.destroy",{lead:O.value}),{onSuccess:()=>E()})},I=s=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",_=p(f.agent_id||""),g=p(f.county_id||""),b=p(f.status||""),k=p(""),m=p({}),ae=(s,o)=>{_.value=s,M(k.value,_.value,g.value,b.value)},ne=(s,o)=>{g.value=s,M(k.value,_.value,g.value,b.value)},le=(s,o)=>{b.value=s,M(k.value,_.value,g.value,b.value)},M=(s,o,t,r)=>{k.value=s,console.log("agentId",o),console.log("countyId",t),console.log("status",r);const c=o===""?null:o,re=t===""?null:t;N.get(route("leads.index",{search:s,agent_id:c,county_id:re,status:r}),{preserveState:!0})},P=(s,o)=>{m.value[s]=o},H=s=>{delete m.value[s]},F=(s,o)=>{R.post(route("leads.update-status",s),{status:o},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete m.value[s];const r=new URLSearchParams(window.location.search).get("page")||1;R.get(route("leads.index"),{search:k.value,agent_id:_.value===""?null:_.value,county_id:g.value===""?null:g.value,page:r},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})},C=p({show:!1,leadId:null,comments:[]}),ie=s=>{C.value={show:!0,leadId:s.id,comments:s.comments||[]}},G=()=>{C.value={show:!1,leadId:null,comments:[]}};return(s,o)=>(a(),l(w,null,[i(v(de),{title:"Leads"}),i(he,null,{default:d(()=>[e("div",ke,[e("div",Ce,[Ae,e("div",Se,[e("div",Me,[Le,L(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>ue(S)?S.value=t:null),onInput:o[1]||(o[1]=t=>M(v(S),_.value,g.value,b.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for leads..."},null,544),[[ce,v(S)]])]),e("div",Ve,[e("button",{onClick:o[2]||(o[2]=t=>y.value="card"),class:V(["px-3 py-2 text-sm font-medium rounded-l-lg",y.value==="card"?"bg-indigo-600 text-white":"text-gray-700 hover:bg-gray-50"])},Ne,2),e("button",{onClick:o[3]||(o[3]=t=>y.value="table"),class:V(["px-3 py-2 text-sm font-medium rounded-r-lg",y.value==="table"?"bg-indigo-600 text-white":"text-gray-700 hover:bg-gray-50"])},De,2)]),u.permissions.canCreateLead?(a(),l("div",Ee,[i(ge,{href:s.route("leads.create")},{default:d(()=>[x(" Add Lead ")]),_:1},8,["href"])])):h("",!0)])]),e("div",Be,[e("div",je,[e("div",Ue,[Oe,i($,{for:"customer_id",value:"Filters"})])]),e("div",Te,[f.isAdmin?(a(),l("div",Ie,[i($,{for:"agent_filter",value:"Agents"}),e("div",Pe,[i(U,{options:J.value,modelValue:_.value,"onUpdate:modelValue":o[4]||(o[4]=t=>_.value=t),onOnchange:ae},null,8,["options","modelValue"])])])):h("",!0),e("div",He,[i($,{for:"county_filter",value:"Country"}),e("div",Fe,[i(U,{options:ee.value,modelValue:g.value,"onUpdate:modelValue":o[5]||(o[5]=t=>g.value=t),onOnchange:ne},null,8,["options","modelValue"])])]),e("div",Ge,[i($,{for:"county_filter",value:"Status"}),e("div",We,[i(U,{options:te.value,modelValue:b.value,"onUpdate:modelValue":o[6]||(o[6]=t=>b.value=t),onOnchange:le},null,8,["options","modelValue"])])])])]),y.value==="card"?(a(),l("div",qe,[(a(!0),l(w,null,A(u.data.data,t=>{var r;return a(),l("div",{key:t.id,class:"bg-white p-5 rounded-lg shadow hover:shadow-md transition"},[e("div",Re,[e("div",null,[e("h3",Ye,n(t.client_name),1),e("p",Ze,"Lead No: "+n(t.lead_number),1)]),e("div",Ke,[m.value[t.id]!==void 0?(a(),l("div",Qe,[L(e("select",{"onUpdate:modelValue":c=>m.value[t.id]=c,onChange:c=>F(t.id,m.value[t.id]),class:"text-sm border-gray-300 rounded px-2 py-1 mt-1"},[(a(),l(w,null,A(D,c=>e("option",{key:c.id,value:c.id},n(c.name),9,Je)),64))],40,Xe),[[W,m.value[t.id]]]),e("button",{onClick:c=>H(t.id),class:"text-gray-400 ml-1 hover:text-gray-600"},"✕",8,et)])):(a(),l("span",{key:1,class:V(["px-3 py-1 rounded-full text-xs font-semibold cursor-pointer mt-1 inline-block",I(t.status)]),onClick:c=>P(t.id,t.status),title:"Click to edit status"},n(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,tt))])]),e("div",st,[e("p",ot,"Country: "+n(t.county?t.county.name:"N/A"),1),e("p",at,"Agent: "+n(((r=t.creator)==null?void 0:r.first_name)||"N/A"),1),e("p",nt,"Date: "+n(new Date(t.created_at).toLocaleDateString("en-GB")),1)]),e("div",lt,[e("p",it,[rt,x(" "+n(t.open_size),1)]),e("p",dt,[ct,x(" "+n(t.email??"-"),1)]),e("p",ut,[mt,x(" "+n(t.number??"-"),1)])]),i(we,{comments:t.comments||[],"current-user-id":s.$page.props.auth.user.id,"is-admin":s.$page.props.auth.user.role_id===1,onAddComment:c=>ie(t)},null,8,["comments","current-user-id","is-admin","onAddComment"]),e("div",ht,[i(v(q),{href:s.route("leads.show",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100"},{default:d(()=>[x(" View ")]),_:2},1032,["href"]),i(v(q),{href:s.route("leads.edit",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-green-600 bg-green-50 rounded-md hover:bg-green-100"},{default:d(()=>[x(" Edit ")]),_:2},1032,["href"]),u.permissions.canDeleteLead?(a(),l("button",{key:0,onClick:c=>T(t.id),class:"flex-1 px-3 py-2 text-xs font-semibold text-red-600 bg-red-50 rounded-md hover:bg-red-100"}," Delete ",8,_t)):h("",!0)])])}),128))])):h("",!0),y.value==="table"?(a(),l("div",gt,[e("div",pt,[e("table",vt,[e("thead",xt,[e("tr",ft,[(a(),l(w,null,A(se,(t,r)=>L(e("th",{key:r,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:c=>v(K)(t.field,t.sortable)},[x(n(t.label)+" ",1),t.sortable?(a(),j(be,{key:0,isSorted:v(Q)===t.field,direction:v(X)},null,8,["isSorted","direction"])):h("",!0)],8,bt),[[me,t.visible]])),64))])]),u.data.data&&u.data.data.length>0?(a(),l("tbody",yt,[(a(!0),l(w,null,A(u.data.data,t=>(a(),l("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",wt,n(t.lead_number),1),e("td",kt,n(t.client_name),1),e("td",Ct,n(t.county?t.county.name:"N/A"),1),e("td",At,n(t.open_size),1),e("td",St,n(t.box_style),1),e("td",Mt,n(t.email??"-"),1),e("td",Lt,n(t.number??"-"),1),e("td",Vt,[m.value[t.id]!==void 0?(a(),l("div",$t,[L(e("select",{"onUpdate:modelValue":r=>m.value[t.id]=r,class:"text-sm border-gray-300 rounded px-2 py-1",onChange:r=>F(t.id,m.value[t.id])},[(a(),l(w,null,A(D,r=>e("option",{class:"text-sm text-gray-900 text-bold",key:r.id,value:r.id},n(r.name),9,zt)),64))],40,Nt),[[W,m.value[t.id]]]),e("button",{onClick:r=>H(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,Dt)])):(a(),l("div",Et,[e("span",{class:V(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",I(t.status)]),onClick:r=>P(t.id,t.status),title:"Click to edit status"},n(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,Bt)]))]),e("td",jt,n(new Date(t.created_at).toLocaleDateString("en-GB")),1),f.isAdmin?(a(),l("td",Ut,n(t.creator?t.creator.first_name:"N/A"),1)):h("",!0),e("td",Ot,[e("div",Tt,[i(_e,{align:"right",width:"48"},{trigger:d(()=>[It]),content:d(()=>[i(Y,{href:s.route("leads.show",{id:t.id})},{svg:d(()=>[Pt]),text:d(()=>[Ht]),_:2},1032,["href"]),u.permissions.canEditLead?(a(),j(Y,{key:0,href:s.route("leads.edit",{id:t.id})},{svg:d(()=>[Ft]),text:d(()=>[Gt]),_:2},1032,["href"])):h("",!0),u.permissions.canDeleteLead?(a(),l("button",{key:1,type:"button",onClick:r=>T(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Yt,8,Wt)):h("",!0)]),_:2},1024)])])]))),128))])):(a(),l("tbody",Zt,Qt))])])])):h("",!0),u.data.data&&u.data.data.length>0?(a(),j(fe,{key:2,class:"mt-6",links:u.data.links},null,8,["links"])):h("",!0)]),i(Z,{show:C.value.show,onClose:G},{default:d(()=>[e("div",Xt,[Jt,i(ye,{"lead-id":C.value.leadId,comments:C.value.comments,"current-user-id":s.$page.props.auth.user.id,"is-admin":s.$page.props.auth.user.role_id===1,onClose:G},null,8,["lead-id","comments","current-user-id","is-admin"])])]),_:1},8,["show"]),i(Z,{show:z.value,onClose:E},{default:d(()=>[e("div",es,[ts,e("div",ss,[i(pe,{onClick:E},{default:d(()=>[x("Cancel")]),_:1}),i(ve,{class:"ml-3",onClick:oe,disabled:v(N).processing},{default:d(()=>[x(" Delete Lead ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{vs as default};
