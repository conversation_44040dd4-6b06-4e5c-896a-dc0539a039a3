import{r as f,b as l,d,e as c,u as A,f as m,F as y,Z as N,h as t,t as s,n as u,l as h,j as i,k as O,g as V,O as z}from"./app-106e7db1.js";import{_ as E,a as v}from"./AdminLayout-fd7c0efe.js";import{M as F}from"./Modal-95b9c727.js";import{_ as U}from"./SecondaryButton-e9368969.js";import{_ as H}from"./CreateButton-ad75d959.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const I={class:"animate-top"},P={class:"flex justify-between items-center mb-6"},q={class:"text-2xl font-semibold leading-7 text-gray-900"},Q=t("p",{class:"text-sm text-gray-600 mt-1"},"Task Details",-1),R={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Y={class:"lg:col-span-2 space-y-6"},Z={class:"bg-white border rounded-lg p-6 bg-white p-4 shadowrounded-lg border"},G=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Task Information",-1),J={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},K=t("label",{class:"block text-sm font-semibold text-gray-900"},"Type",-1),W={class:"mt-1"},X=t("label",{class:"block text-sm font-semibold text-gray-900"},"Priority",-1),tt={class:"mt-1"},et=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),st={class:"mt-1"},ot=t("label",{class:"block text-sm font-semibold text-gray-900"},"Assigned To",-1),at={class:"mt-1 text-sm text-gray-700"},lt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Due Date",-1),dt={key:0,class:"ml-2 text-red-500"},nt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),it={class:"mt-1 text-sm text-gray-700"},rt={key:0,class:"mt-6"},ct=t("label",{class:"block text-sm font-semibold text-gray-900 mb-2"},"Description",-1),mt={class:"bg-gray-50 rounded-lg p-4"},ut={class:"text-sm text-gray-700 whitespace-pre-wrap"},gt={key:1,class:"mt-6"},ht=t("label",{class:"block text-sm font-semibold text-gray-900 mb-2"},"Notes",-1),xt={class:"bg-gray-50 rounded-lg p-4"},bt={class:"text-sm text-gray-700 whitespace-pre-wrap"},_t={key:0,class:"bg-white border rounded-lg p-6"},kt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Related Lead",-1),ft={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},yt={class:"flex items-center"},vt=t("div",{class:"flex-shrink-0"},[t("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1),wt={class:"ml-4"},pt={class:"text-sm font-medium text-blue-900"},Ct={class:"text-sm text-blue-700"},Tt={key:0},St={key:1},Dt={key:2},Mt={key:1,class:"bg-white border rounded-lg p-6"},jt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Activity History",-1),$t={class:"space-y-4"},Bt={class:"flex-shrink-0"},Lt={class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},At={class:"text-xs"},Nt={class:"flex-1"},Ot={class:"text-sm font-medium text-gray-900"},Vt={class:"text-sm text-gray-600"},zt={class:"text-xs text-gray-500 mt-1"},Et={class:"space-y-6"},Ft={class:"bg-white border rounded-lg p-6"},Ut=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1),Ht={class:"space-y-3"},It=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),h(" Edit Task ")],-1),Pt=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),h(" Back to List ")],-1),qt={class:"bg-white border rounded-lg p-6"},Qt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Timeline",-1),Rt={class:"space-y-3"},Yt={class:"flex items-center text-sm"},Zt=t("div",{class:"w-2 h-2 bg-blue-500 rounded-full mr-3"},null,-1),Gt=t("div",{class:"font-medium"},"Created",-1),Jt={class:"text-gray-700"},Kt={class:"flex items-center text-sm"},Wt=t("div",{class:"font-medium"},"Due Date",-1),Xt={key:0,class:"flex items-center text-sm"},te=t("div",{class:"w-2 h-2 bg-green-500 rounded-full mr-3"},null,-1),ee=t("div",{class:"font-medium"},"Completed",-1),se={class:"text-gray-700"},oe={class:"p-6"},ae=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to complete this task? ",-1),le={class:"mt-6 flex justify-end space-x-4"},ge={__name:"Show",props:{task:Object},setup(e){const w=e,g=o=>{const a=new Date(o),n=String(a.getDate()).padStart(2,"0"),b=String(a.getMonth()+1).padStart(2,"0"),$=a.getFullYear(),B=String(a.getHours()).padStart(2,"0"),L=String(a.getMinutes()).padStart(2,"0");return`${n}/${b}/${$} ${B}:${L}`},p=o=>o.replace("_"," ").replace(/\b\w/g,a=>a.toUpperCase()),C=o=>o.replace("_"," ").replace(/\b\w/g,a=>a.toUpperCase()),r=o=>new Date(o)<new Date&&w.task.status!=="completed",T=o=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[o]||"bg-gray-100 text-gray-800",S=o=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",D=o=>({pending:"bg-yellow-100 text-yellow-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",x=f(!1),_=f(null),M=o=>{_.value=o,x.value=!0},k=()=>{x.value=!1},j=()=>{_.value;const o=route("tasks.complete",_.value);V.post(o).then(a=>{console.log("Task completed successfully",a.data.message),x.value=!1,z.reload()}).catch(a=>{var n,b;console.error("Error completing task:",((b=(n=a.response)==null?void 0:n.data)==null?void 0:b.error)||a.message)})};return(o,a)=>(l(),d(y,null,[c(A(N),{title:"Tasks"}),c(E,null,{default:m(()=>[t("div",I,[t("div",P,[t("div",null,[t("h2",q,s(e.task.title),1),Q])]),t("div",R,[t("div",Y,[t("div",Z,[G,t("div",J,[t("div",null,[K,t("div",W,[t("span",{class:u(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",T(e.task.type)])},s(p(e.task.type)),3)])]),t("div",null,[X,t("div",tt,[t("span",{class:u(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",S(e.task.priority)])},s(e.task.priority.toUpperCase()),3)])]),t("div",null,[et,t("div",st,[t("span",{class:u(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",D(e.task.status)])},s(C(e.task.status)),3)])]),t("div",null,[ot,t("div",at,s(e.task.assigned_to.first_name)+" "+s(e.task.assigned_to.last_name),1)]),t("div",null,[lt,t("div",{class:u(["mt-1 text-sm",{"text-red-600 font-semibold":r(e.task.due_date),"text-gray-900":!r(e.task.due_date)}])},[h(s(g(e.task.due_date))+" ",1),r(e.task.due_date)?(l(),d("span",dt,"⚠️ Overdue")):i("",!0)],2)]),t("div",null,[nt,t("div",it,s(e.task.created_by.first_name)+" "+s(e.task.created_by.last_name),1)])]),e.task.description?(l(),d("div",rt,[ct,t("div",mt,[t("p",ut,s(e.task.description),1)])])):i("",!0),e.task.notes?(l(),d("div",gt,[ht,t("div",xt,[t("p",bt,s(e.task.notes),1)])])):i("",!0)]),e.task.lead?(l(),d("div",_t,[kt,t("div",ft,[t("div",yt,[vt,t("div",wt,[t("div",pt," 📋 Lead: "+s(e.task.lead.client_name),1),t("div",Ct,[e.task.lead.county?(l(),d("span",Tt,s(e.task.lead.county.name),1)):i("",!0),e.task.lead.number?(l(),d("span",St,"📞 "+s(e.task.lead.number),1)):i("",!0),e.task.lead.email?(l(),d("span",Dt,"📧 "+s(e.task.lead.email),1)):i("",!0)])])])])])):i("",!0),e.task.activity_logs&&e.task.activity_logs.length>0?(l(),d("div",Mt,[jt,t("div",$t,[(l(!0),d(y,null,O(e.task.activity_logs,n=>(l(),d("div",{key:n.id,class:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"},[t("div",Bt,[t("div",Lt,[t("span",At,s(n.user.first_name.charAt(0)),1)])]),t("div",Nt,[t("div",Ot,s(n.user.first_name)+" "+s(n.user.last_name),1),t("div",Vt,s(n.description),1),t("div",zt,s(g(n.created_at)),1)])]))),128))])])):i("",!0)]),t("div",Et,[t("div",Ft,[Ut,t("div",Ht,[e.task.status!=="completed"?(l(),d("button",{key:0,onClick:a[0]||(a[0]=n=>M(e.task.id)),class:"w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700"}," ✅ Mark as Complete ")):i("",!0),c(v,{href:o.route("tasks.edit",e.task.id),class:"w-full"},{svg:m(()=>[It]),_:1},8,["href"]),c(v,{href:o.route("tasks.index"),class:"w-full"},{svg:m(()=>[Pt]),_:1},8,["href"])])]),t("div",qt,[Qt,t("div",Rt,[t("div",Yt,[Zt,t("div",null,[Gt,t("div",Jt,s(g(e.task.created_at)),1)])]),t("div",Kt,[t("div",{class:u(["w-2 h-2 rounded-full mr-3",{"bg-red-500":r(e.task.due_date),"bg-yellow-500":!r(e.task.due_date)}])},null,2),t("div",null,[Wt,t("div",{class:u({"text-red-500":r(e.task.due_date),"text-gray-700":!r(e.task.due_date)})},s(g(e.task.due_date)),3)])]),e.task.completed_at?(l(),d("div",Xt,[te,t("div",null,[ee,t("div",se,s(g(e.task.completed_at)),1)])])):i("",!0)])])])])]),c(F,{show:x.value,onClose:k},{default:m(()=>[t("div",oe,[ae,t("div",le,[c(U,{onClick:k},{default:m(()=>[h("Cancel")]),_:1}),c(H,{class:"w-44",onClick:j},{default:m(()=>[h(" Complete Task ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{ge as default};
