import{_ as S}from"./AdminLayout-fd7c0efe.js";import{T as C,C as k,c as j,b as i,d as c,e as x,u as A,f as M,F as h,Z as T,h as t,t as s,D as _,k as g,j as O,n as z}from"./app-106e7db1.js";import"./_plugin-vue_export-helper-c27b6911.js";const U={class:"animate-top"},D=t("div",{class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},[t("div",null,[t("h1",{class:"text-3xl font-bold text-gray-900"},"Dashboard"),t("p",{class:"text-gray-600 mt-1"},"Complete overview of your business performance")])],-1),R={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},q={class:"bg-white overflow-hidden shadow rounded-lg"},V={class:"p-5"},B={class:"flex items-center"},L=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})])])],-1),H={class:"ml-5 w-0 flex-1"},K=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Leads",-1),F={class:"text-lg font-semibold text-gray-900"},P={class:"bg-white overflow-hidden shadow rounded-lg"},Q={class:"p-5"},$={class:"flex items-center"},I=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM8 15a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})])])],-1),G={class:"ml-5 w-0 flex-1"},N=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Orders",-1),E={class:"text-lg font-semibold text-gray-900"},J={class:"bg-white overflow-hidden shadow rounded-lg"},W={class:"p-5"},Z={class:"flex items-center"},X=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z","clip-rule":"evenodd"})])])],-1),Y={class:"ml-5 w-0 flex-1"},tt=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Quotations",-1),et={class:"text-lg font-semibold text-gray-900"},st={class:"bg-white overflow-hidden shadow rounded-lg"},ot={class:"p-5"},lt={class:"flex items-center"},dt=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})])])],-1),nt={class:"ml-5 w-0 flex-1"},at=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Task",-1),it={class:"text-lg font-semibold text-gray-900"},ct={class:"bg-white shadow rounded-lg p-6 mb-8"},rt=t("h3",{class:"text-lg font-semibold text-gray-900 mb-6"},"Revenue by Country",-1),ut={class:"mb-6"},ht=t("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Total Sales Revenue",-1),_t={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},mt={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},xt={class:"flex items-center"},gt=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),vt={class:"ml-3"},ft=t("p",{class:"text-sm font-medium text-blue-600"},"UK Sales",-1),bt={class:"text-lg font-bold text-blue-900"},yt={class:"bg-green-50 border border-green-200 rounded-lg p-4"},pt={class:"flex items-center"},wt=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),St={class:"ml-3"},Ct=t("p",{class:"text-sm font-medium text-green-600"},"US Sales",-1),kt={class:"text-lg font-bold text-green-900"},jt={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},At={class:"flex items-center"},Mt=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),Tt={class:"ml-3"},Ot=t("p",{class:"text-sm font-medium text-purple-600"},"Canada Sales",-1),zt={class:"text-lg font-bold text-purple-900"},Ut={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},Dt={class:"flex items-center"},Rt=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),qt={class:"ml-3"},Vt=t("p",{class:"text-sm font-medium text-yellow-600"},"Australia Sales",-1),Bt={class:"text-lg font-bold text-yellow-900"},Lt=t("h4",{class:"text-md font-medium text-gray-900 mb-4"},"This Month Sales Revenue",-1),Ht={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Kt={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},Ft={class:"flex items-center"},Pt=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),Qt={class:"ml-3"},$t=t("p",{class:"text-sm font-medium text-blue-600"},"UK This Month",-1),It={class:"text-lg font-bold text-blue-900"},Gt={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Nt={class:"flex items-center"},Et=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),Jt={class:"ml-3"},Wt=t("p",{class:"text-sm font-medium text-green-600"},"US This Month",-1),Zt={class:"text-lg font-bold text-green-900"},Xt={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},Yt={class:"flex items-center"},te=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),ee={class:"ml-3"},se=t("p",{class:"text-sm font-medium text-purple-600"},"Canada This Month",-1),oe={class:"text-lg font-bold text-purple-900"},le={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},de={class:"flex items-center"},ne=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1),ae={class:"ml-3"},ie=t("p",{class:"text-sm font-medium text-yellow-600"},"Australia This Month",-1),ce={class:"text-lg font-bold text-yellow-900"},re={class:"bg-white shadow rounded-lg p-6 mb-8"},ue=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Conversion Rates",-1),he={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},_e={class:"text-center"},me={class:"text-3xl font-bold text-blue-600"},xe=t("div",{class:"text-sm text-gray-500"},"Lead → Quotation",-1),ge={class:"text-center"},ve={class:"text-3xl font-bold text-green-600"},fe=t("div",{class:"text-sm text-gray-500"},"Quotation → Order",-1),be={class:"text-center"},ye={class:"text-3xl font-bold text-purple-600"},pe=t("div",{class:"text-sm text-gray-500"},"Lead → Order",-1),we={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},Se={class:"bg-white shadow rounded-lg p-6"},Ce=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Sales Pipeline",-1),ke={class:"space-y-4"},je={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},Ae=t("span",null,"Leads",-1),Me={class:"w-full bg-gray-200 rounded-full h-2"},Te={class:"flex justify-between text-xs text-gray-500 mt-1"},Oe={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},ze=t("span",null,"Quotations",-1),Ue={class:"w-full bg-gray-200 rounded-full h-2"},De={class:"flex justify-between text-xs text-gray-500 mt-1"},Re={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},qe=t("span",null,"Orders",-1),Ve={class:"w-full bg-gray-200 rounded-full h-2"},Be={class:"flex justify-between text-xs text-gray-500 mt-1"},Le={class:"bg-white shadow rounded-lg p-6"},He=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Status Distribution",-1),Ke={class:"space-y-3"},Fe={class:"flex items-center justify-between"},Pe=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-blue-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Confirmed")],-1),Qe={class:"text-sm font-medium text-gray-900"},$e={class:"flex items-center justify-between"},Ie=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-purple-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Under Production")],-1),Ge={class:"text-sm font-medium text-gray-900"},Ne={class:"flex items-center justify-between"},Ee=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-indigo-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Shipped")],-1),Je={class:"text-sm font-medium text-gray-900"},We={class:"flex items-center justify-between"},Ze=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-green-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Delivered")],-1),Xe={class:"text-sm font-medium text-gray-900"},Ye={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},ts={class:"lg:col-span-2 bg-white shadow rounded-lg p-6"},es=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Recent Activities",-1),ss={class:"flow-root"},os={class:"-mb-8"},ls={key:0,class:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"},ds={class:"relative flex space-x-3"},ns={class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},as=["d"],is={class:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4"},cs={class:"text-sm text-gray-900"},rs={class:"text-sm text-gray-500"},us={class:"text-right text-sm whitespace-nowrap text-gray-500"},hs={class:"bg-white shadow rounded-lg p-6"},_s=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Top Performing Agents",-1),ms={class:"space-y-4"},xs={class:"text-sm font-medium text-gray-900"},gs={class:"text-xs text-gray-500"},vs={class:"text-right"},fs={class:"text-sm font-medium text-green-600"},ws={__name:"Dashboard",props:{recentOrders:Array,orderStats:Object,countryRevenue:Object,leadStats:Object,quotationStats:Object,monthlyTrends:Array,topAgents:Array,recentActivities:Array,permissions:Object,taskStats:Object},setup(e){const d=e;C({}),k(async()=>{localStorage.setItem("permissions",JSON.stringify(d.permissions))});const a=(n,l="United Kingdom")=>{const o={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},r=Object.keys(o).find(w=>l==null?void 0:l.toLowerCase().includes(w.toLowerCase())),{locale:y,currency:m}=o[r]||o["United Kingdom"],p=new Intl.NumberFormat(y,{style:"currency",currency:m,currencyDisplay:"symbol"}).format(n);return`${m} ${p}`},v=n=>new Date(n).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),f=n=>{const l={lead:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z",quotation:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",order:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"};return l[n]||l.lead},b=n=>{const l={lead:"text-blue-600 bg-blue-100",quotation:"text-green-600 bg-green-100",order:"text-purple-600 bg-purple-100"};return l[n]||l.lead},u=j(()=>{const n=d.leadStats.total>0?(d.quotationStats.total/d.leadStats.total*100).toFixed(1):0,l=d.quotationStats.total>0?(d.orderStats.total/d.quotationStats.total*100).toFixed(1):0,o=d.leadStats.total>0?(d.orderStats.total/d.leadStats.total*100).toFixed(1):0;return{leadToQuotation:n,quotationToOrder:l,leadToOrder:o}});return(n,l)=>(i(),c(h,null,[x(A(T),{title:"Dashboard"}),x(S,null,{default:M(()=>[t("div",U,[D,t("div",R,[t("div",q,[t("div",V,[t("div",B,[L,t("div",H,[t("dl",null,[K,t("dd",F,s(e.leadStats.total),1)])])])])]),t("div",P,[t("div",Q,[t("div",$,[I,t("div",G,[t("dl",null,[N,t("dd",E,s(e.orderStats.total),1)])])])])]),t("div",J,[t("div",W,[t("div",Z,[X,t("div",Y,[t("dl",null,[tt,t("dd",et,s(e.quotationStats.total),1)])])])])]),t("div",st,[t("div",ot,[t("div",lt,[dt,t("div",nt,[t("dl",null,[at,t("dd",it,s(e.taskStats.total),1)])])])])])]),t("div",ct,[rt,t("div",ut,[ht,t("div",_t,[t("div",mt,[t("div",xt,[gt,t("div",vt,[ft,t("p",bt,s(a(e.countryRevenue.total.uk,"United Kingdom")),1)])])]),t("div",yt,[t("div",pt,[wt,t("div",St,[Ct,t("p",kt,s(a(e.countryRevenue.total.us,"United States")),1)])])]),t("div",jt,[t("div",At,[Mt,t("div",Tt,[Ot,t("p",zt,s(a(e.countryRevenue.total.canada,"Canada")),1)])])]),t("div",Ut,[t("div",Dt,[Rt,t("div",qt,[Vt,t("p",Bt,s(a(e.countryRevenue.total.australia,"Australia")),1)])])])])]),t("div",null,[Lt,t("div",Ht,[t("div",Kt,[t("div",Ft,[Pt,t("div",Qt,[$t,t("p",It,s(a(e.countryRevenue.monthly.uk,"United Kingdom")),1)])])]),t("div",Gt,[t("div",Nt,[Et,t("div",Jt,[Wt,t("p",Zt,s(a(e.countryRevenue.monthly.us,"United States")),1)])])]),t("div",Xt,[t("div",Yt,[te,t("div",ee,[se,t("p",oe,s(a(e.countryRevenue.monthly.canada,"Canada")),1)])])]),t("div",le,[t("div",de,[ne,t("div",ae,[ie,t("p",ce,s(a(e.countryRevenue.monthly.australia,"Australia")),1)])])])])])]),t("div",re,[ue,t("div",he,[t("div",_e,[t("div",me,s(u.value.leadToQuotation)+"%",1),xe]),t("div",ge,[t("div",ve,s(u.value.quotationToOrder)+"%",1),fe]),t("div",be,[t("div",ye,s(u.value.leadToOrder)+"%",1),pe])])]),t("div",we,[t("div",Se,[Ce,t("div",ke,[t("div",null,[t("div",je,[Ae,t("span",null,s(e.leadStats.total),1)]),t("div",Me,[t("div",{class:"bg-blue-600 h-2 rounded-full",style:_(`width: ${e.leadStats.total>0?100:0}%`)},null,4)]),t("div",Te,[t("span",null,"New: "+s(e.leadStats.new),1),t("span",null,"Won: "+s(e.leadStats.won),1)])]),t("div",null,[t("div",Oe,[ze,t("span",null,s(e.quotationStats.total),1)]),t("div",Ue,[t("div",{class:"bg-green-600 h-2 rounded-full",style:_(`width: ${e.quotationStats.total>0?e.quotationStats.total/Math.max(e.leadStats.total,1)*100:0}%`)},null,4)]),t("div",De,[t("span",null,"Pending: "+s(e.quotationStats.pending),1),t("span",null,"Order Placed: "+s(e.quotationStats.order_placed),1)])]),t("div",null,[t("div",Re,[qe,t("span",null,s(e.orderStats.total),1)]),t("div",Ve,[t("div",{class:"bg-purple-600 h-2 rounded-full",style:_(`width: ${e.orderStats.total>0?e.orderStats.total/Math.max(e.leadStats.total,1)*100:0}%`)},null,4)]),t("div",Be,[t("span",null,"Confirmed: "+s(e.orderStats.confirmed),1),t("span",null,"Delivered: "+s(e.orderStats.delivered),1)])])])]),t("div",Le,[He,t("div",Ke,[t("div",Fe,[Pe,t("span",Qe,s(e.orderStats.confirmed),1)]),t("div",$e,[Ie,t("span",Ge,s(e.orderStats.under_production),1)]),t("div",Ne,[Ee,t("span",Je,s(e.orderStats.shipped),1)]),t("div",We,[Ze,t("span",Xe,s(e.orderStats.delivered),1)])])])]),t("div",Ye,[t("div",ts,[es,t("div",ss,[t("ul",os,[(i(!0),c(h,null,g(e.recentActivities,(o,r)=>(i(),c("li",{key:r,class:"relative pb-8"},[r!==e.recentActivities.length-1?(i(),c("div",ls)):O("",!0),t("div",ds,[t("div",null,[t("span",{class:z(["h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white",b(o.type)])},[(i(),c("svg",ns,[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:f(o.type)},null,8,as)]))],2)]),t("div",is,[t("div",null,[t("p",cs,s(o.title),1),t("p",rs,s(o.description),1)]),t("div",us,[t("time",null,s(v(o.created_at)),1)])])])]))),128))])])]),t("div",hs,[_s,t("div",ms,[(i(!0),c(h,null,g(e.topAgents,o=>(i(),c("div",{key:o.id,class:"flex items-center justify-between"},[t("div",null,[t("p",xs,s(o.first_name)+" "+s(o.last_name),1),t("p",gs,s(o.orders_count)+" orders",1)]),t("div",vs,[t("p",fs,s(a(o.orders_sum_total_amount)),1)])]))),128))])])])])]),_:1})],64))}};export{ws as default};
