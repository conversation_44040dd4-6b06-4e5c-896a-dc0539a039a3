import{T as w,b as p,d as c,e as o,u as t,f as u,F as S,Z as T,h as a,y as U,l as D,s as O}from"./app-106e7db1.js";import{_ as $,a as A}from"./AdminLayout-fd7c0efe.js";import{_ as i}from"./InputLabel-a6c15f23.js";import{a as r,_}from"./TextInput-566b8743.js";import{_ as g}from"./TextArea-4aff7158.js";import{_ as m}from"./SearchableDropdownNew-4174a3dc.js";import{P as C}from"./PrimaryButton-71d6fb9f.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},P={class:"flex justify-between items-center mb-6"},j=a("div",null,[a("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Task"),a("p",{class:"text-sm text-gray-600 mt-1"},"Update task details and status")],-1),I={class:"flex space-x-2"},N=["onSubmit"],E={class:"grid grid-cols-1 md:grid-cols-12 gap-6"},q={class:"md:col-span-3"},F={class:"relative mt-2"},L={class:"md:col-span-3"},M={class:"md:col-span-3"},Q={class:"relative mt-2"},W={class:"md:col-span-3"},Z={class:"relative mt-2"},z={class:"md:col-span-6"},G={class:"md:col-span-6"},H={class:"md:col-span-4"},J={class:"md:col-span-4"},K={class:"relative mt-2"},R={class:"md:col-span-4"},X={class:"relative mt-2"},Y={class:"bg-gray-50 p-4 rounded-lg"},ee=a("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Quick Status Updates:",-1),te={class:"grid grid-cols-2 md:grid-cols-4 gap-2"},se={class:"flex mt-6 items-center justify-between"},oe={class:"ml-auto flex items-center justify-end gap-x-6"},ae=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),de={key:0},le={key:1},ge={__name:"Edit",props:{task:Object,users:Array,types:Array,priority:Array,leads:Array,status:Array},setup(y){const l=y,e=w({title:l.task.title||"",description:l.task.description||"",type:l.task.type||"",priority:l.task.priority||"medium",status:l.task.status||"pending",due_date:l.task.due_date?new Date(l.task.due_date).toISOString().slice(0,16):"",reminder_date:l.task.reminder_date?new Date(l.task.reminder_date).toISOString().slice(0,16):"",assigned_to:l.task.assigned_to||"",lead_id:l.task.lead_id||null,notes:l.task.notes||""}),f=(n,s)=>e.lead_id=n,v=(n,s)=>e.type=n,b=(n,s)=>e.priority=n,k=(n,s)=>e.assigned_to=n,x=(n,s)=>e.status=n,V=()=>{const n=new Date(e.due_date);if(n.setDate(n.getDate()+1),e.due_date=n.toISOString().slice(0,16),e.reminder_date){const s=new Date(e.reminder_date);s.setDate(s.getDate()+1),e.reminder_date=s.toISOString().slice(0,16)}},h=()=>{e.put(route("tasks.update",l.task.id),{preserveScroll:!0})};return(n,s)=>(p(),c(S,null,[o(t(T),{title:"Tasks"}),o($,null,{default:u(()=>[a("div",B,[a("div",P,[j,a("div",I,[o(t(U),{href:n.route("tasks.index"),class:"px-4 py-2 bg-slate-100 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100"},{default:u(()=>[D(" ← Back to Tasks ")]),_:1},8,["href"])])]),a("form",{onSubmit:O(h,["prevent"]),class:"space-y-6"},[a("div",E,[a("div",q,[o(i,{for:"lead_id",value:"Lead"}),a("div",F,[o(m,{options:l.leads,modelValue:t(e).lead_id,"onUpdate:modelValue":s[0]||(s[0]=d=>t(e).lead_id=d),onOnchange:f},null,8,["options","modelValue"])]),o(r,{message:t(e).errors.lead_id},null,8,["message"])]),a("div",L,[o(i,{for:"title",value:"Task Title *"}),o(_,{id:"title",modelValue:t(e).title,"onUpdate:modelValue":s[1]||(s[1]=d=>t(e).title=d),type:"text",required:"",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{message:t(e).errors.title},null,8,["message"])]),a("div",M,[o(i,{for:"type",value:"Task Type *"}),a("div",Q,[o(m,{options:l.types,modelValue:t(e).type,"onUpdate:modelValue":s[2]||(s[2]=d=>t(e).type=d),onOnchange:v},null,8,["options","modelValue"])]),o(r,{message:t(e).errors.type},null,8,["message"])]),a("div",W,[o(i,{for:"priority",value:"Priority *"}),a("div",Z,[o(m,{options:l.priority,modelValue:t(e).priority,"onUpdate:modelValue":s[3]||(s[3]=d=>t(e).priority=d),onOnchange:b},null,8,["options","modelValue"])]),o(r,{message:t(e).errors.priority},null,8,["message"])]),a("div",z,[o(i,{for:"description",value:"Description"}),o(g,{id:"description",modelValue:t(e).description,"onUpdate:modelValue":s[4]||(s[4]=d=>t(e).description=d),rows:3},null,8,["modelValue"]),o(r,{message:t(e).errors.description},null,8,["message"])]),a("div",G,[o(i,{for:"notes",value:"Additional Notes"}),o(g,{id:"notes",modelValue:t(e).notes,"onUpdate:modelValue":s[5]||(s[5]=d=>t(e).notes=d),rows:3},null,8,["modelValue"]),o(r,{message:t(e).errors.notes},null,8,["message"])]),a("div",H,[o(i,{for:"due_date",value:"Due Date & Time *"}),o(_,{id:"due_date",type:"datetime-local",modelValue:t(e).due_date,"onUpdate:modelValue":s[6]||(s[6]=d=>t(e).due_date=d),required:"",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{message:t(e).errors.due_date},null,8,["message"])]),a("div",J,[o(i,{for:"assigned_to",value:"Assign To *"}),a("div",K,[o(m,{options:l.users,modelValue:t(e).assigned_to,"onUpdate:modelValue":s[7]||(s[7]=d=>t(e).assigned_to=d),onOnchange:k},null,8,["options","modelValue"])]),o(r,{message:t(e).errors.assigned_to},null,8,["message"])]),a("div",R,[o(i,{for:"status",value:"Status *"}),a("div",X,[o(m,{options:l.status,modelValue:t(e).status,"onUpdate:modelValue":s[8]||(s[8]=d=>t(e).status=d),onOnchange:x},null,8,["options","modelValue"])]),o(r,{message:t(e).errors.status},null,8,["message"])])]),a("div",Y,[ee,a("div",te,[a("button",{type:"button",onClick:s[9]||(s[9]=d=>t(e).status="in_progress"),class:"px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"},"🔄 Start Working"),a("button",{type:"button",onClick:s[10]||(s[10]=d=>t(e).status="completed"),class:"px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200"},"✅ Mark Complete"),a("button",{type:"button",onClick:V,class:"px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200"},"⏰ Postpone +1 Day"),a("button",{type:"button",onClick:s[11]||(s[11]=d=>t(e).status="cancelled"),class:"px-3 py-2 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200"},"❌ Cancel Task")])]),a("div",se,[a("div",oe,[o(A,{href:n.route("tasks.index")},{svg:u(()=>[ae]),_:1},8,["href"]),o(C,{disabled:t(e).processing},{default:u(()=>[t(e).processing?(p(),c("span",de,"Updating...")):(p(),c("span",le,"Update"))]),_:1},8,["disabled"])])])],40,N)])]),_:1})],64))}};export{ge as default};
