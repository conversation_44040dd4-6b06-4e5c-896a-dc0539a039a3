import{r as u,T as _,b as y,d as w,e as o,f as a,h as s,J as h,l as c,u as t,L as x,n as g}from"./app-106e7db1.js";import{D as m}from"./DangerButton-d7fb5ad6.js";import{_ as v,a as k}from"./TextInput-566b8743.js";import{_ as D}from"./InputLabel-a6c15f23.js";import{M as C}from"./Modal-95b9c727.js";import{_ as b}from"./SecondaryButton-e9368969.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const V={class:"space-y-6"},B=s("header",null,[s("h2",{class:"text-lg font-medium text-gray-900"},"Delete Account"),s("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain. ")],-1),U={class:"p-6"},A=s("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete your account? ",-1),$=s("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ",-1),K={class:"mt-6"},M={class:"mt-6 flex justify-end"},J={__name:"DeleteUserForm",setup(N){const r=u(!1),l=u(null),e=_({password:""}),p=()=>{r.value=!0,h(()=>l.value.focus())},d=()=>{e.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>n(),onError:()=>l.value.focus(),onFinish:()=>e.reset()})},n=()=>{r.value=!1,e.reset()};return(P,i)=>(y(),w("section",V,[B,o(m,{onClick:p},{default:a(()=>[c("Delete Account")]),_:1}),o(C,{show:r.value,onClose:n},{default:a(()=>[s("div",U,[A,$,s("div",K,[o(D,{for:"password",value:"Password",class:"sr-only"}),o(v,{id:"password",ref_key:"passwordInput",ref:l,modelValue:t(e).password,"onUpdate:modelValue":i[0]||(i[0]=f=>t(e).password=f),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",onKeyup:x(d,["enter"])},null,8,["modelValue","onKeyup"]),o(k,{message:t(e).errors.password,class:"mt-2"},null,8,["message"])]),s("div",M,[o(b,{onClick:n},{default:a(()=>[c(" Cancel ")]),_:1}),o(m,{class:g(["ml-3",{"opacity-25":t(e).processing}]),disabled:t(e).processing,onClick:d},{default:a(()=>[c(" Delete Account ")]),_:1},8,["class","disabled"])])])]),_:1},8,["show"])]))}};export{J as default};
