import{r as p,b as s,d as a,e as r,u as I,f as u,F as h,Z as A,h as t,t as o,i as B,z as M,k as b,n as z,j as i,l as v,O as P}from"./app-106e7db1.js";import{_ as $,a as w}from"./AdminLayout-fd7c0efe.js";import{C as D,L as j}from"./LeadComments-02a1a569.js";import{M as Q}from"./Modal-95b9c727.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const V={class:"animate-top"},E={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},O={class:"text-3xl font-bold text-gray-900"},U=t("p",{class:"text-gray-600 mt-1"},"Leads Details",-1),F={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},H={class:"lg:col-span-2 space-y-8"},R={class:"bg-white shadow rounded-lg p-6"},T=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1),W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Z=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),G={class:"mt-1 text-sm text-gray-700"},J=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1),K={class:"mt-1 text-sm text-gray-700"},X=t("label",{class:"block text-sm font-semibold text-gray-900"},"Email",-1),Y={class:"mt-1 text-sm text-gray-700"},tt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Number",-1),et={class:"mt-1 text-sm text-gray-700"},st=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1),at={key:0,class:"flex items-center space-x-2 w-full"},ot=["value"],lt={key:1,class:"flex items-center space-x-2"},nt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1),dt={class:"mt-1 text-sm text-gray-700"},it={class:"bg-white shadow rounded-lg p-6"},ct=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1),rt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},mt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1),xt={class:"mt-1 text-sm text-gray-700"},ut=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1),gt={class:"mt-1 text-sm text-gray-700"},ht=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1),yt={class:"mt-1 text-sm text-gray-700"},_t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1),ft={class:"mt-1 text-sm text-gray-700"},pt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1),bt={class:"mt-1 text-sm text-gray-700"},wt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1),vt={class:"mt-1 text-sm text-gray-700"},kt={key:0,class:"md:col-span-2"},Ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1),St={class:"mt-1 text-sm text-gray-700"},Lt={class:"bg-white shadow rounded-lg p-6"},qt=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quantity Details",-1),Nt={class:"overflow-x-auto"},It={class:"min-w-full divide-y divide-gray-200"},At=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Requested"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Confirmed"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Status")])],-1),Bt={class:"bg-white divide-y divide-gray-200"},Mt={key:0},zt=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 1",-1),Pt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Dt={key:0,class:"text-green-600 font-medium"},jt={key:1,class:"text-gray-400"},Qt={class:"px-6 py-4 whitespace-nowrap"},Vt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Et={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},Ot={key:1},Ut=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 2",-1),Ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Rt={key:0,class:"text-green-600 font-medium"},Tt={key:1,class:"text-gray-400"},Wt={class:"px-6 py-4 whitespace-nowrap"},Zt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Gt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},Jt={key:2},Kt=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 3",-1),Xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Yt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},te={key:0,class:"text-green-600 font-medium"},ee={key:1,class:"text-gray-400"},se={class:"px-6 py-4 whitespace-nowrap"},ae={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},oe={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},le={key:3},ne=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 4",-1),de={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ie={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ce={key:0,class:"text-green-600 font-medium"},re={key:1,class:"text-gray-400"},me={class:"px-6 py-4 whitespace-nowrap"},xe={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},ue={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},ge={key:0,class:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"},he={class:"flex items-center"},ye=t("svg",{class:"w-5 h-5 text-green-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1),_e=t("p",{class:"text-sm font-medium text-green-800"},"Order Confirmed",-1),fe={class:"text-xs text-green-600"},pe={key:0,class:"bg-white shadow rounded-lg p-6"},be=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1),we={class:"text-sm text-gray-700 whitespace-pre-wrap"},ve={class:"space-y-6"},ke={class:"bg-white shadow rounded-lg p-6"},Ce=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),Se={class:"space-y-3 w-full"},Le=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),v(" Edit Lead ")],-1),qe=t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),v(" Back to List ")],-1),Ne={key:0,class:"bg-white shadow rounded-lg p-6"},Ie=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Documents",-1),Ae={class:"space-y-3"},Be={class:"flex items-center space-x-3"},Me=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1),ze={class:"text-sm text-gray-700"},Pe=["href"],$e={class:"bg-white shadow rounded-lg p-6"},De=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Comments",-1),je={class:"bg-white shadow rounded-lg p-6"},Qe=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1),Ve={class:"space-y-4"},Ee={class:"flex items-start space-x-3"},Oe=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1),Ue=t("p",{class:"text-sm font-semibold text-gray-900"},"leads Created",-1),Fe={class:"text-xs text-gray-500"},He={key:0,class:"flex items-start space-x-3"},Re=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1),Te=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1),We={class:"text-xs text-gray-500"},Ze={class:"bg-white rounded-lg p-6 w-full"},Ge=t("h3",{class:"text-lg font-medium mb-4"},"Add Comment",-1),ss={__name:"Show",props:{leads:{type:Object,required:!0}},setup(e){const k=l=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[l]||"bg-gray-100 text-gray-800",g=l=>l?new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",C=[{id:"new",name:"New"},{id:"contacted",name:"Contacted"},{id:"quotation",name:"Quotation"},{id:"negotiation",name:"Negotiation"},{id:"won",name:"Won"},{id:"lost",name:"Lost"}],c=p({}),S=(l,n)=>{P.post(route("leads.update-status",l),{status:n},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete c.value[l],new URLSearchParams(window.location.search).get("page")},onError:x=>{console.error("Update failed:",x),alert("Failed to update status. Please try again.")}})},L=l=>{delete c.value[l]},q=(l,n)=>{c.value[l]=n},m=p({show:!1,leadId:null,comments:[]}),N=l=>{m.value={show:!0,leadId:l.id,comments:l.comments||[]}},y=()=>{m.value={show:!1,leadId:null,comments:[]}};return(l,n)=>(s(),a(h,null,[r(I(A),{title:"Leads"}),r($,null,{default:u(()=>{var x,_,f;return[t("div",V,[t("div",E,[t("div",null,[t("h1",O,o(e.leads.lead_number),1),U])]),t("div",F,[t("div",H,[t("div",R,[T,t("div",W,[t("div",null,[Z,t("p",G,o(e.leads.client_name),1)]),t("div",null,[J,t("p",K,o(((x=e.leads.county)==null?void 0:x.name)||"N/A"),1)]),t("div",null,[X,t("p",Y,o(e.leads.email??"N/A"),1)]),t("div",null,[tt,t("p",et,o(e.leads.number??"N/A"),1)]),t("div",null,[st,c.value[e.leads.id]!==void 0?(s(),a("div",at,[B(t("select",{"onUpdate:modelValue":n[0]||(n[0]=d=>c.value[e.leads.id]=d),class:"text-sm border-gray-300 rounded px-2 py-1",onChange:n[1]||(n[1]=d=>S(e.leads.id,c.value[e.leads.id]))},[(s(),a(h,null,b(C,d=>t("option",{class:"text-sm text-gray-900 text-bold",key:d.id,value:d.id},o(d.name),9,ot)),64))],544),[[M,c.value[e.leads.id]]]),t("button",{onClick:n[2]||(n[2]=d=>L(e.leads.id)),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ")])):(s(),a("div",lt,[t("span",{class:z(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",k(e.leads.status)]),onClick:n[3]||(n[3]=d=>q(e.leads.id,e.leads.status)),title:"Click to edit status"},o(e.leads.status.charAt(0).toUpperCase()+e.leads.status.slice(1)),3)]))]),t("div",null,[nt,t("p",dt,o((_=e.leads.creator)==null?void 0:_.first_name)+" "+o((f=e.leads.creator)==null?void 0:f.last_name),1)])])]),t("div",it,[ct,t("div",rt,[t("div",null,[mt,t("p",xt,o(e.leads.dimensions),1)]),t("div",null,[ut,t("p",gt,o(e.leads.open_size),1)]),t("div",null,[ht,t("p",yt,o(e.leads.box_style),1)]),t("div",null,[_t,t("p",ft,o(e.leads.stock),1)]),t("div",null,[pt,t("p",bt,o(e.leads.lamination),1)]),t("div",null,[wt,t("p",vt,o(e.leads.printing),1)]),e.leads.add_ons?(s(),a("div",kt,[Ct,t("p",St,o(e.leads.add_ons),1)])):i("",!0)])]),t("div",Lt,[qt,t("div",Nt,[t("table",It,[At,t("tbody",Bt,[e.leads.qty_1?(s(),a("tr",Mt,[zt,t("td",Pt,o(parseInt(e.leads.qty_1).toLocaleString())+" pcs",1),t("td",$t,[e.leads.confirmed_qty_1?(s(),a("span",Dt,o(parseInt(e.leads.confirmed_qty_1).toLocaleString())+" pcs ",1)):(s(),a("span",jt,"Not confirmed"))]),t("td",Qt,[e.leads.confirmed_qty_1?(s(),a("span",Vt," ✓ Confirmed ")):(s(),a("span",Et," Pending "))])])):i("",!0),e.leads.qty_2?(s(),a("tr",Ot,[Ut,t("td",Ft,o(parseInt(e.leads.qty_2).toLocaleString())+" pcs",1),t("td",Ht,[e.leads.confirmed_qty_2?(s(),a("span",Rt,o(parseInt(e.leads.confirmed_qty_2).toLocaleString())+" pcs ",1)):(s(),a("span",Tt,"Not confirmed"))]),t("td",Wt,[e.leads.confirmed_qty_2?(s(),a("span",Zt," ✓ Confirmed ")):(s(),a("span",Gt," Pending "))])])):i("",!0),e.leads.qty_3?(s(),a("tr",Jt,[Kt,t("td",Xt,o(parseInt(e.leads.qty_3).toLocaleString())+" pcs",1),t("td",Yt,[e.leads.confirmed_qty_3?(s(),a("span",te,o(parseInt(e.leads.confirmed_qty_3).toLocaleString())+" pcs ",1)):(s(),a("span",ee,"Not confirmed"))]),t("td",se,[e.leads.confirmed_qty_3?(s(),a("span",ae," ✓ Confirmed ")):(s(),a("span",oe," Pending "))])])):i("",!0),e.leads.qty_4?(s(),a("tr",le,[ne,t("td",de,o(parseInt(e.leads.qty_4).toLocaleString())+" pcs",1),t("td",ie,[e.leads.confirmed_qty_4?(s(),a("span",ce,o(parseInt(e.leads.confirmed_qty_4).toLocaleString())+" pcs ",1)):(s(),a("span",re,"Not confirmed"))]),t("td",me,[e.leads.confirmed_qty_4?(s(),a("span",xe," ✓ Confirmed ")):(s(),a("span",ue," Pending "))])])):i("",!0)])])]),e.leads.order_confirmed_at?(s(),a("div",ge,[t("div",he,[ye,t("div",null,[_e,t("p",fe,o(g(e.leads.order_confirmed_at)),1)])])])):i("",!0)]),e.leads.notes?(s(),a("div",pe,[be,t("p",we,o(e.leads.notes),1)])):i("",!0)]),t("div",ve,[t("div",ke,[Ce,t("div",Se,[r(w,{href:l.route("leads.edit",e.leads.id),class:"w-full"},{svg:u(()=>[Le]),_:1},8,["href"]),r(w,{href:l.route("leads.index"),class:"w-full"},{svg:u(()=>[qe]),_:1},8,["href"])])]),e.leads.documents&&e.leads.documents.length>0?(s(),a("div",Ne,[Ie,t("div",Ae,[(s(!0),a(h,null,b(e.leads.documents,d=>(s(),a("div",{key:d.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",Be,[Me,t("span",ze,o(d.orignal_name),1)]),t("a",{href:"/uploads/leads/"+d.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,Pe)]))),128))])])):i("",!0),t("div",$e,[De,r(D,{comments:e.leads.comments||[],"current-user-id":l.$page.props.auth.user.id,"is-admin":l.$page.props.auth.user.role_id===1,onAddComment:n[4]||(n[4]=d=>N(l.lead))},null,8,["comments","current-user-id","is-admin"])]),t("div",je,[Qe,t("div",Ve,[t("div",Ee,[Oe,t("div",null,[Ue,t("p",Fe,o(g(e.leads.created_at)),1)])]),e.leads.updated_at!==e.leads.created_at?(s(),a("div",He,[Re,t("div",null,[Te,t("p",We,o(g(e.leads.updated_at)),1)])])):i("",!0)])])])])]),r(Q,{show:m.value.show,onClose:y},{default:u(()=>[t("div",Ze,[Ge,r(j,{"lead-id":m.value.leadId,comments:m.value.comments,"current-user-id":l.$page.props.auth.user.id,"is-admin":l.$page.props.auth.user.role_id===1,onClose:y},null,8,["lead-id","comments","current-user-id","is-admin"])])]),_:1},8,["show"])]}),_:1})],64))}};export{ss as default};
