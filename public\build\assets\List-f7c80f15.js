import{r as f,c as D,b as a,d as n,e as l,u as b,f as _,F as U,Z as ue,h as e,i as $,v as me,G as pe,j as A,k as T,q as B,l as E,t as i,H as _e,z as ge,n as z,O as F}from"./app-106e7db1.js";import{_ as ve,b as he,a as I}from"./AdminLayout-fd7c0efe.js";import{_ as fe}from"./SecondaryButton-e9368969.js";import{D as ye}from"./DangerButton-d7fb5ad6.js";import{M as xe}from"./Modal-95b9c727.js";import{s as be,_ as we}from"./Pagination-ab577825.js";import{_ as M}from"./SearchableDropdownNew-4174a3dc.js";import{_ as O}from"./InputLabel-a6c15f23.js";import{_ as ke}from"./ArrowIcon-7e47bcc0.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const Ce={class:"animate-top"},Ae={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},Se=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Orders")],-1),Oe={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},Ve={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},De=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),Ue={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},Me={class:"flex justify-between mb-2"},Pe={class:"flex"},Le=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Ne={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},$e={key:0,class:"sm:col-span-3"},Te={class:"relative mt-2"},Be={class:"sm:col-span-3"},Ee={class:"relative mt-2"},je={class:"sm:col-span-3"},ze={class:"relative mt-2"},Fe={class:"sm:col-span-3"},Ie={class:"relative mt-2"},Re={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Ge={class:"shadow rounded-lg"},qe={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ke={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},We={class:"border-b-2"},He=["onClick"],Qe={key:0},Ye={class:"px-4 py-2.5 min-w-36"},Xe={class:"px-4 py-2.5 min-w-36"},Ze={class:"px-4 py-2.5 min-w-28"},Je={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},et={class:"px-4 py-2.5 min-w-36"},tt={class:"px-4 py-2.5 font-semibold text-green-600 min-w-36"},st={class:"px-4 py-2.5"},ot={key:0,class:"text-gray-700 text-sm"},at={key:1,class:"text-gray-400 text-sm"},lt={class:"px-4 py-2.5 min-w-36"},nt={class:"px-4 py-2.5 min-w-44"},it={key:0,class:"flex items-center space-x-2"},rt=["onUpdate:modelValue","onChange"],dt=["value"],ct=["onClick"],ut={key:1,class:"flex items-center space-x-2"},mt=["onClick"],pt={class:"px-4 py-2.5 min-w-40"},_t={class:"flex items-center space-x-2 min-w-40"},gt={key:0,class:"px-4 py-2.5"},vt={class:"items-center px-4 py-2.5"},ht={class:"flex items-center justify-start gap-4"},ft=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),yt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),xt=e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1),bt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),wt=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),kt=["onClick"],Ct=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),At=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),St=[Ct,At],Ot=["onClick"],Vt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),Dt=e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1),Ut=[Vt,Dt],Mt={key:1},Pt=e("tr",{class:"bg-white"},[e("td",{colspan:"10",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Lt=[Pt],Nt={class:"p-6"},$t=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this order? ",-1),Tt={class:"mt-6 flex justify-end"},Qt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","productionStages","production_stage","isAdmin"],setup(g){const d=g,{form:P,search:V,sort:R,fetchData:Bt,sortKey:G,sortDirection:q}=be("orders.index"),L=f(!1),j=f(null),K=[{id:"confirmed",name:"Confirmed"},{id:"under_production",name:"Under Production"},{id:"shipped",name:"Shipped"},{id:"delivered",name:"Delivered"}],W=D(()=>[{id:"",name:"All Agents"},...d.agents]),H=D(()=>[{id:"",name:"All Country"},...d.counties]),Q=D(()=>[{id:"",name:"All Stages"},...d.productionStages]),Y=D(()=>[{id:"",name:"All Status"},...d.statusOptions]),X=[{field:"order_number",label:"ORDER NO",sortable:!0,visible:!0},{field:"quotation.quotation_number",label:"QUOTATION NO",sortable:!1,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!1,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"lead.county.name",label:"COUNTRY",sortable:!0,visible:!0},{field:"total_amount",label:"TOTAL AMOUNT",sortable:!0,visible:!0},{field:"tracking_number",label:"TRACKING",sortable:!1,visible:!0},{field:"expected_delivery",label:"EXP. DELIVERY",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"production_stage",label:"PRODUCTION STAGE",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:d.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],Z=s=>{j.value=s,L.value=!0},N=()=>{L.value=!1},J=()=>{P.delete(route("orders.destroy",{order:j.value}),{onSuccess:()=>N()})},ee=s=>({confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-yellow-100 text-yellow-800",delivered:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",te=s=>({"Pre-press":"bg-blue-100 text-blue-800",Printing:"bg-purple-100 text-purple-800",Lamination:"bg-yellow-100 text-yellow-800","Die cutting":"bg-green-100 text-green-800","Quality assurance":"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",u=f(d.agent_id||""),m=f(d.county_id||""),p=f(d.status||""),v=f(d.production_stage||""),w=f(""),y=f({}),se=(s,o)=>{u.value=s,S(w.value,u.value,m.value,p.value,v.value)},oe=(s,o)=>{m.value=s,S(w.value,u.value,m.value,p.value,v.value)},ae=(s,o)=>{p.value=s,S(w.value,u.value,m.value,p.value,v.value)},le=(s,o)=>{v.value=s,S(w.value,u.value,m.value,p.value,v.value)},S=(s,o,t,c,h)=>{w.value=s;const x=o===""?null:o,k=t===""?null:t,C=c===""?null:c,r=h===""?null:h;P.get(route("orders.index",{search:s,agent_id:x,county_id:k,status:C,production_stage:r}),{preserveState:!0})},ne=(s,o)=>{y.value[s]=o},ie=s=>{delete y.value[s]},re=s=>{window.open(route("orders.pdf",s),"_blank")},de=(s,o)=>{F.post(route("orders.update-status",s),{status:o},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete y.value[s];const c=new URLSearchParams(window.location.search).get("page")||1;F.get(route("orders.index"),{search:w.value,agent_id:u.value===""?null:u.value,county_id:m.value===""?null:m.value,status:p.value===""?null:p.value,page:c},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})},ce=(s,o)=>{const t={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},c=Object.keys(t).find(C=>o==null?void 0:o.toLowerCase().includes(C.toLowerCase())),{locale:h,currency:x}=t[c]||t["United Kingdom"],k=new Intl.NumberFormat(h,{style:"currency",currency:x,currencyDisplay:"symbol"}).format(s);return`${x} ${k}`};return(s,o)=>(a(),n(U,null,[l(b(ue),{title:"Orders"}),l(ve,null,{default:_(()=>[e("div",Ce,[e("div",Ae,[Se,e("div",Oe,[e("div",Ve,[De,$(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=t=>pe(V)?V.value=t:null),onInput:o[1]||(o[1]=t=>S(b(V),u.value,m.value,p.value,v.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for orders..."},null,544),[[me,b(V)]])])])]),e("div",Ue,[e("div",Me,[e("div",Pe,[Le,l(O,{for:"filters",value:"Filters"})])]),e("div",Ne,[d.isAdmin?(a(),n("div",$e,[l(O,{for:"agent_filter",value:"Agents"}),e("div",Te,[l(M,{options:W.value,modelValue:u.value,"onUpdate:modelValue":o[2]||(o[2]=t=>u.value=t),onOnchange:se},null,8,["options","modelValue"])])])):A("",!0),e("div",Be,[l(O,{for:"county_filter",value:"Country"}),e("div",Ee,[l(M,{options:H.value,modelValue:m.value,"onUpdate:modelValue":o[3]||(o[3]=t=>m.value=t),onOnchange:oe},null,8,["options","modelValue"])])]),e("div",je,[l(O,{for:"status_filter",value:"Status"}),e("div",ze,[l(M,{options:Y.value,modelValue:p.value,"onUpdate:modelValue":o[4]||(o[4]=t=>p.value=t),onOnchange:ae},null,8,["options","modelValue"])])]),e("div",Fe,[l(O,{for:"production_stage_filter",value:"Production Stage"}),e("div",Ie,[l(M,{options:Q.value,modelValue:v.value,"onUpdate:modelValue":o[5]||(o[5]=t=>v.value=t),onOnchange:le},null,8,["options","modelValue"])])])])]),e("div",Re,[e("div",Ge,[e("table",qe,[e("thead",Ke,[e("tr",We,[(a(),n(U,null,T(X,(t,c)=>$(e("th",{key:c,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:h=>b(R)(t.field,t.sortable)},[E(i(t.label)+" ",1),t.sortable?(a(),B(ke,{key:0,isSorted:b(G)===t.field,direction:b(q)},null,8,["isSorted","direction"])):A("",!0)],8,He),[[_e,t.visible]])),64))])]),g.data.data&&g.data.data.length>0?(a(),n("tbody",Qe,[(a(!0),n(U,null,T(g.data.data,t=>{var c,h,x,k,C;return a(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ye,i(t.order_number),1),e("td",Xe,i(t.quotation?t.quotation.quotation_number:"N/A"),1),e("td",Ze,i(t.lead?t.lead.lead_number:"N/A"),1),e("td",Je,i((c=t.lead)==null?void 0:c.client_name),1),e("td",et,i((x=(h=t.lead)==null?void 0:h.county)==null?void 0:x.name),1),e("td",tt,i(ce(t.total_amount,(C=(k=t.lead)==null?void 0:k.county)==null?void 0:C.name)),1),e("td",st,[t.tracking_number?(a(),n("span",ot,i(t.tracking_number),1)):(a(),n("span",at,"-"))]),e("td",lt,i(t.expected_delivery?new Date(t.expected_delivery).toLocaleDateString("en-GB"):"N/A"),1),e("td",nt,[y.value[t.id]!==void 0?(a(),n("div",it,[$(e("select",{"onUpdate:modelValue":r=>y.value[t.id]=r,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:r=>de(t.id,y.value[t.id])},[(a(),n(U,null,T(K,r=>e("option",{key:r.id,value:r.id},i(r.name),9,dt)),64))],40,rt),[[ge,y.value[t.id]]]),e("button",{onClick:r=>ie(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," ✕ ",8,ct)])):(a(),n("div",ut,[e("span",{class:z(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",ee(t.status)]),onClick:r=>ne(t.id,t.status),title:"Click to edit status"},i(t.status.charAt(0).toUpperCase()+t.status.slice(1).replace("_"," ")),11,mt)]))]),e("td",pt,[e("div",_t,[e("span",{class:z(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",te(t.production_stage)])},i(t.production_stage??"N/A"),3)])]),d.isAdmin?(a(),n("td",gt,i(t.lead.creator?t.lead.creator.first_name:"N/A"),1)):A("",!0),e("td",vt,[e("div",ht,[l(he,{align:"right",width:"48"},{trigger:_(()=>[ft]),content:_(()=>[l(I,{href:s.route("orders.show",{order:t.id})},{svg:_(()=>[yt]),text:_(()=>[xt]),_:2},1032,["href"]),g.permissions.canEditOrder?(a(),B(I,{key:0,href:s.route("orders.edit",{order:t.id})},{svg:_(()=>[bt]),text:_(()=>[wt]),_:2},1032,["href"])):A("",!0),g.permissions.canDeleteOrder?(a(),n("button",{key:1,type:"button",onClick:r=>Z(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},St,8,kt)):A("",!0),e("button",{type:"button",onClick:r=>re(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ut,8,Ot)]),_:2},1024)])])])}),128))])):(a(),n("tbody",Mt,Lt))])])]),g.data.data&&g.data.data.length>0?(a(),B(we,{key:0,class:"mt-6",links:g.data.links},null,8,["links"])):A("",!0)]),l(xe,{show:L.value,onClose:N},{default:_(()=>[e("div",Nt,[$t,e("div",Tt,[l(fe,{onClick:N},{default:_(()=>[E("Cancel")]),_:1}),l(ye,{class:"ml-3",onClick:J,disabled:b(P).processing},{default:_(()=>[E(" Delete Order ")]),_:1},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Qt as default};
