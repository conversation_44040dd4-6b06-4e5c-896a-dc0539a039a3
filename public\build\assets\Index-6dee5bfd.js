import{r as w,b as i,d,e as n,u as x,f as c,F as T,Z as q,h as e,y as f,l as u,j as m,t as a,k as G,q as M,O as j,g as Y,n as v}from"./app-106e7db1.js";import{_ as Z}from"./AdminLayout-fd7c0efe.js";import{_ as b}from"./SearchableDropdownNew-4174a3dc.js";import{_ as y}from"./InputLabel-a6c15f23.js";import{_ as B}from"./CreateButton-ad75d959.js";import{s as J,_ as K}from"./Pagination-ab577825.js";import{M as Q}from"./Modal-95b9c727.js";import{_ as R}from"./SecondaryButton-e9368969.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const W={class:"animate-top"},X={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},ee={class:"flex justify-between items-center mb-6"},te=e("div",null,[e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Tasks"),e("p",{class:"text-sm text-gray-600 mt-1"},"Manage your tasks and follow-ups")],-1),se={class:"flex space-x-2"},oe={key:0,class:"sm:flex-none"},le={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ne={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},re={class:"flex items-center"},ae=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1),ie={class:"ml-4"},de=e("p",{class:"text-sm font-medium text-blue-600"},"Total Tasks",-1),ce={class:"text-2xl font-semibold text-blue-900"},ue={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},me={class:"flex items-center"},ge=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1),xe={class:"ml-4"},he=e("p",{class:"text-sm font-medium text-yellow-600"},"Pending",-1),_e={class:"text-2xl font-semibold text-yellow-900"},pe={class:"bg-red-50 border border-red-200 rounded-lg p-4"},fe={class:"flex items-center"},ve=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1),be={class:"ml-4"},ye=e("p",{class:"text-sm font-medium text-red-600"},"Overdue",-1),ke={class:"text-2xl font-semibold text-red-900"},we={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Ce={class:"flex items-center"},Ve=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1),Te={class:"ml-4"},Me=e("p",{class:"text-sm font-medium text-green-600"},"Due Today",-1),je={class:"text-2xl font-semibold text-green-900"},Be={class:"bg-gray-50 p-4 rounded-lg mb-6"},Oe={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Se={class:"relative mt-2"},$e={class:"relative mt-2"},De={class:"relative mt-2"},Ae={class:"relative mt-2"},ze={key:0,class:"text-center py-12"},Ne=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),Ue=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No tasks found",-1),Ee=e("p",{class:"mt-1 text-sm text-gray-500"},"Get started by creating a new task.",-1),He={class:"mt-6"},Fe=e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Pe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6"},Ie={class:"flex items-start justify-between"},Le={class:"flex-1"},qe={class:"text-sm font-semibold text-gray-900 mb-1"},Ge={key:0,class:"text-xs text-gray-500"},Ye={class:"flex flex-col items-end space-y-2"},Ze={class:"space-y-2 mb-4"},Je={class:"flex items-center text-sm text-gray-700"},Ke=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1),Qe=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),Re={key:0,class:"ml-1 text-red-500"},We={class:"flex items-center text-xs text-gray-700"},Xe={class:"flex space-x-2"},et=["onClick"],tt=["onClick"],st={class:"p-6"},ot=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to complete this task? ",-1),lt={class:"mt-6 flex justify-end space-x-4"},ht={__name:"Index",props:{tasks:Object,users:Array,stats:Object,status:Array,types:Array,priorities:Array,permissions:Object},setup(l){const{form:O}=J("tasks.index"),r=w({status:"",type:"",priority:"",assigned_to:""}),S=s=>{r.value.status=s,h()},$=s=>{r.value.type=s,h()},D=s=>{r.value.priority=s,h()},A=s=>{r.value.assigned_to=s,h()},h=()=>{j.get(route("tasks.index"),r.value,{preserveState:!0,preserveScroll:!0})},_=w(!1),C=w(null),z=s=>{C.value=s,_.value=!0},V=()=>{_.value=!1},N=()=>{const s=C.value,o=route("tasks.complete",s);Y.post(o).then(t=>{console.log("Task completed successfully",t.data.message),_.value=!1,j.reload()}).catch(t=>{var g,p;console.error("Error completing task:",((p=(g=t.response)==null?void 0:g.data)==null?void 0:p.error)||t.message)})},U=s=>{const o=new Date(s),t=String(o.getDate()).padStart(2,"0"),g=String(o.getMonth()+1).padStart(2,"0"),p=o.getFullYear();return`${t}/${g}/${p}`},E=s=>s.replace("_"," ").replace(/\b\w/g,o=>o.toUpperCase()),H=s=>s.replace("_"," ").replace(/\b\w/g,o=>o.toUpperCase()),k=s=>s.status!=="completed"&&new Date(s.due_date)<new Date,F=s=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[s]||"bg-gray-100 text-gray-800",P=s=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",I=s=>({pending:"bg-yellow-100 text-yellow-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",L=s=>{O.delete(route("tasks.destroy",{task:s}))};return(s,o)=>(i(),d(T,null,[n(x(q),{title:"Tasks"}),n(Z,null,{default:c(()=>[e("div",W,[e("div",X,[e("div",ee,[te,e("div",se,[n(x(f),{href:s.route("tasks.dashboard"),class:"inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700"},{default:c(()=>[u(" 📊 Dashboard ")]),_:1},8,["href"]),l.permissions.canCreateTask?(i(),d("div",oe,[n(B,{href:s.route("tasks.create")},{default:c(()=>[u(" Add Task ")]),_:1},8,["href"])])):m("",!0)])]),e("div",le,[e("div",ne,[e("div",re,[ae,e("div",ie,[de,e("p",ce,a(l.stats.total),1)])])]),e("div",ue,[e("div",me,[ge,e("div",xe,[he,e("p",_e,a(l.stats.pending),1)])])]),e("div",pe,[e("div",fe,[ve,e("div",be,[ye,e("p",ke,a(l.stats.overdue),1)])])]),e("div",we,[e("div",Ce,[Ve,e("div",Te,[Me,e("p",je,a(l.stats.due_today),1)])])])]),e("div",Be,[e("div",Oe,[e("div",null,[n(y,{for:"agent_filter",value:"Status"}),e("div",Se,[n(b,{options:l.status,modelValue:r.value.status,"onUpdate:modelValue":o[0]||(o[0]=t=>r.value.status=t),onOnchange:S},null,8,["options","modelValue"])])]),e("div",null,[n(y,{for:"agent_filter",value:"Type"}),e("div",$e,[n(b,{options:l.types,modelValue:r.value.type,"onUpdate:modelValue":o[1]||(o[1]=t=>r.value.type=t),onOnchange:$},null,8,["options","modelValue"])])]),e("div",null,[n(y,{for:"agent_filter",value:"Priority"}),e("div",De,[n(b,{options:l.priorities,modelValue:r.value.priority,"onUpdate:modelValue":o[2]||(o[2]=t=>r.value.priority=t),onOnchange:D},null,8,["options","modelValue"])])]),e("div",null,[n(y,{for:"agent_filter",value:"Assigned To"}),e("div",Ae,[n(b,{options:l.users,modelValue:r.value.assigned_to,"onUpdate:modelValue":o[3]||(o[3]=t=>r.value.assigned_to=t),onOnchange:A},null,8,["options","modelValue"])])])])]),l.tasks.data.length===0?(i(),d("div",ze,[Ne,Ue,Ee,e("div",He,[n(x(f),{href:s.route("tasks.create"),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"},{default:c(()=>[Fe,u(" New Task ")]),_:1},8,["href"])])])):m("",!0)]),e("div",Pe,[(i(!0),d(T,null,G(l.tasks.data,t=>(i(),d("div",{key:t.id,class:"bg-white border rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",Ie,[e("div",Le,[e("h3",qe,a(t.title),1),t.lead?(i(),d("p",Ge," 📋 "+a(t.lead.client_name)+" ("+a(t.lead.company_name)+") ",1)):m("",!0)]),e("div",Ye,[e("span",{class:v(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",P(t.priority)])},a(t.priority),3),e("span",{class:v(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",I(t.status)])},a(H(t.status)),3)])]),e("div",Ze,[e("div",Je,[Ke,u(" "+a(t.assigned_to.first_name)+" "+a(t.assigned_to.last_name),1)]),e("div",{class:v(["flex items-center text-xs",{"text-red-600 font-semibold":k(t),"text-gray-600":!k(t)}])},[Qe,u(" "+a(U(t.due_date))+" ",1),k(t)?(i(),d("span",Re,"⚠️ Overdue")):m("",!0)],2),e("div",We,[e("span",{class:v(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",F(t.type)])},a(E(t.type)),3)])]),e("div",Xe,[n(x(f),{href:s.route("tasks.show",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100"},{default:c(()=>[u(" View ")]),_:2},1032,["href"]),l.permissions.canEditTask?(i(),M(x(f),{key:0,href:s.route("tasks.edit",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-green-600 bg-green-50 rounded-md hover:bg-green-100"},{default:c(()=>[u(" Edit ")]),_:2},1032,["href"])):m("",!0),t.status!=="completed"&&l.permissions.canEditTask?(i(),d("button",{key:1,onClick:g=>z(t.id),class:"flex-1 px-3 py-2 text-xs font-semibold text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"}," Complete ",8,et)):m("",!0),l.permissions.canDeleteTask?(i(),d("button",{key:2,onClick:g=>L(t.id),class:"flex-1 px-3 py-2 text-xs font-semibold text-red-600 bg-red-50 rounded-md hover:bg-red-100"}," Delete ",8,tt)):m("",!0)])]))),128))]),l.tasks.links&&l.tasks.links.length>0?(i(),M(K,{key:0,class:"mt-6",links:l.tasks.links},null,8,["links"])):m("",!0)]),n(Q,{show:_.value,onClose:V},{default:c(()=>[e("div",st,[ot,e("div",lt,[n(R,{onClick:V},{default:c(()=>[u("Cancel")]),_:1}),n(B,{class:"w-44",onClick:N},{default:c(()=>[u(" Complete Task ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{ht as default};
